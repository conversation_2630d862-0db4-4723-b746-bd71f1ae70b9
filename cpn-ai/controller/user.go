package controller

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/myimg"
	"cpn-ai/common/shortid"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"cpn-ai/service/guide"
	"cpn-ai/structs"
	"encoding/json"
	"fmt"
	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"net/http"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"time"
)

type userApi_ struct {
}

var UserApi userApi_

type loginReq struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type loginScanReq struct {
	Ticket string `json:"ticket"`
}

type getTokenReq struct {
	Mobile string `json:"mobile"`
	Pw     string `json:"pw"`
}

type loginSmsReq struct {
	Mobile      string `json:"mobile"`
	SmsCode     string `json:"sms_code"`
	InvitedCode string `json:"invited_code"`
	UserEnv     string `json:"user_env"`
	Ticket      string `json:"ticket"`
}

type resetPasswordReq struct {
	Mobile   string `json:"mobile"`
	Password string `json:"password"`
	SmsCode  string `json:"sms_code"`
}

type setUserInfoReq struct {
	Action       string `json:"action"`
	Mobile       string `json:"mobile"`
	SmsCode      string `json:"sms_code"`
	DisplayName  string `json:"display_name"`
	AvatarBase64 string `json:"avatar_base64"`
	CertName     string `json:"cert_name"`
	CertNo       string `json:"cert_no"`
	CertType     string `json:"cert_type"`
	GuideId      int    `json:"guide_id"`
}

type UserInfoResp struct {
	UserId          uint            `json:"-"`
	Username        string          `json:"username"`
	DisplayName     string          `json:"display_name"`
	AvatarUrl       string          `json:"avatar_url"`
	Mobile          string          `json:"mobile"`
	CertName        string          `json:"cert_name"`
	CertNo          string          `json:"cert_no"`
	Amount          decimal.Decimal `json:"amount"`
	Reward          decimal.Decimal `json:"reward"`
	RewardAmount    decimal.Decimal `json:"reward_amount"`
	RewardFrozen    decimal.Decimal `json:"reward_frozen"`
	InvitationCode  string          `json:"invitation_code"`
	ValidCardCount  int             `json:"valid_card_count"`
	ValidCardAmount decimal.Decimal `json:"valid_card_amount"`
	UserType        int             `json:"user_type"`
	Insider         int             `json:"insider,omitempty"`
	Student         string          `json:"student,omitempty"`
	Company         string          `json:"company,omitempty"`
	GuideStatus     int             `json:"guide_status"`
	WeixinBind      string          `json:"weixin_bind"`
}

func LoginQr(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	postUrl := "http://*************:5102/api/v1/notify/wechat/official/qr"
	postData := make(map[string]interface{})
	//get access_token error : errcode=40164 , errormsg=invalid ip ************** ipv6 ::ffff:**************, not in whitelist rid: 67f1b275-1ac4625f-3c847038
	if ginH, err := service.GetNodeForGin(0, postUrl, postData); err != nil {
		msg = "获取登录二维码失败，请点击左上角手机图标切换登录方式"
		//msg = err.Error()
		logger.Error(msg, " err:", err, " ginH:", ginH)
	} else {
		if r, err := service.ResultGinH(ginH); err == nil {
			if strings.Contains(r.Msg, "not in whitelist") {
				msg = "获取登录二维码失败，请点击左上角手机图标切换登录方式"
				return
			}
		}
		c.JSON(http.StatusOK, ginH)
		code = 0
	}
}

func LoginScan(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq loginScanReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		return
	}

	if oReq.Ticket == "" {
		msg = "参数错误"
		return
	}

	key := enums.RedisKeyEnum.ScanLogin + oReq.Ticket
	val, _ := common.RedisGet(key)
	if val == "" {
		msg = "等待扫码"
		code = 0
		return
	}

	var scanLogin structs.ScanLogin
	if err := utils.GetStructFromJson(&scanLogin, val); err != nil {
		logger.Error(val, "  err:", err)
		msg = "解析登录数据失败"
		return
	}

	if scanLogin.ErrMsg != "" {
		logger.Error("扫描登录失败：", val)
		msg = "此二维码已被使用过"
		return
	}

	if scanLogin.Mobile == "" {
		msg = "第一次扫码登录，需要绑定手机"
		code = enums.ErrCodeActionEnum.ScanBindMobile
		result["ticket"] = oReq.Ticket
		return
	}
	var user model.User
	if err := user.GetByMobile(scanLogin.Mobile); err != nil {
		logger.Error(val, " err:", err)
		msg = "查询手机号码失败"
		return
	}

	if user.ID == 0 {
		msg = "获取用户信息失败"
		logger.Error(msg, "mobile:", scanLogin.Mobile)
		return
	}

	if user.Status != 1 {
		msg = "账号不可用"
		logger.Error(msg, "userId:", user.ID)
		return
	}

	if user.AccountId == 0 {
		if scanLogin.AccountId > 0 {
			if err := user.SetAccountId(scanLogin.AccountId); err != nil {
				logger.Error("绑定手机mobile:", user.Mobile, " val:", val)
			} else {
				logger.Info("绑定手机mobile:", user.Mobile, " 账号ID设置成功 ")
			}
		}
	}

	if user.AccessToken == "" {
		user.AccessToken = utils.GetUUID()
		if err := user.SetAccessToken(user.AccessToken); err != nil {
			msg = "设置Access失败"
			logger.Error(msg, err)
			return
		}
	}

	if token, err := middleware.NewJWT().SetToken(user.ID, user.Username, user.Mobile, user.DisplayName, user.Role, user.AccessToken, 0); err != nil {
		logger.Error(err)
		msg = "生成Token失败"
		return
	} else {
		result["token"] = token
	}

	resp := UserInfoResp{
		UserId:      user.ID,
		Username:    user.Username,
		DisplayName: user.DisplayName,
		Mobile:      utils.FormatMobileStar(user.Mobile),
	}
	result["user"] = resp
	msg = "登录成功"
	code = 0

	go func() {
		clientIp := utils.GetClientIp(c.Request.Header)
		if err := user.SetLastLogin(clientIp); err != nil {
			logger.Error("设置最后登录信息失败", err)
		}
		if _, err := service.LocationService.PushJson("user", user.ID); err != nil {
			logger.Error(err)
		}
	}()
}

func LoginSms(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq loginSmsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "无效的参数"
		return
	}

	if oReq.UserEnv == "" {
		oReq.UserEnv = "{}"
	}

	if !utils.IsMobile(oReq.Mobile) {
		msg = "手机号码不正确"
		return
	}

	if len(oReq.SmsCode) != 4 {
		msg = "短信验证码不正确"
		return
	}

	redisKey := enums.RedisKeyEnum.SmsLogin + oReq.Mobile
	//common.RedisSet(redisKey+":testcount", strconv.Itoa(1), time.Minute*10) //测试

	testCount, _ := common.RedisGet(redisKey + ":testcount")
	if testCount == "" {
		msg = "验证码已失效，请10分钟后重试"
		logger.Error(msg, oReq.Mobile)
		return
	}
	iTestCount, _ := strconv.Atoi(testCount)
	if iTestCount > 10 {
		msg = "验证码尝试次数过多，请10分钟后重试"
		logger.Error(msg, oReq.Mobile)
		return
	}
	iTestCount = iTestCount + 1
	if err := common.RedisSet(redisKey+":testcount", strconv.Itoa(iTestCount), time.Minute*10); err != nil {
		msg = "设置短信验证码尝试次数失败"
		logger.Error(msg, oReq.Mobile)
		return
	}

	//common.RedisSet(enums.RedisKeyEnum.SmsLogin+loginReq.Mobile, "1234", time.Minute*10) //测试
	v, _ := common.RedisGet(enums.RedisKeyEnum.SmsLogin + oReq.Mobile)

	if len(v) == 0 {
		msg = "验证码已失效，请重试"
		logger.Error(msg)
		return
	}
	if v != oReq.SmsCode {
		msg = "短信验证码不正确"
		logger.Error(msg)
		return
	}

	commandKey := "LoginSms_Mobile_" + oReq.Mobile
	if val, ok := service.TaskRunning.Load(commandKey); ok {
		runningCommand := val.(structs.RunningCommand)
		if runningCommand.StartTime.Time().Before(time.Now().Add(time.Minute * -1)) {
			service.TaskRunning.Delete(commandKey)
		} else {
			msg = "请勿重复提交"
			logger.Error(msg, utils.GetJsonFromStruct(runningCommand))
			return
		}
	}

	commandRunningValue := structs.RunningCommand{Command: "短信验证码登录", Msg: oReq.Mobile, StartTime: jsontime.Now()}
	commandLog := utils.GetJsonFromStruct(commandRunningValue)
	logger.Info(commandLog)
	service.TaskRunning.Store(commandKey, commandRunningValue)
	defer func() {
		service.TaskRunning.Delete(commandKey)
	}()

	var user model.User
	if v == oReq.SmsCode {
		if err := user.GetByMobile(oReq.Mobile); err != nil {
			if err != gorm.ErrRecordNotFound {
				msg = "查询手机号码出错"
				logger.Error(err)
				return
			}
		}
	}

	clientIp := utils.GetClientIp(c.Request.Header)
	if user.ID == 0 {
		exists, err := user.ExistsMobile(oReq.Mobile)
		if err != nil || exists {
			msg = "查询数据出错"
			logger.Error(err, " ", exists)
			return
		}

		var invitationUser model.User
		if oReq.InvitedCode != "" {
			if err := invitationUser.GetByInvitationCode(oReq.InvitedCode); err != nil {
				logger.Error(err)
			} else {
				user.InvitedCode = oReq.InvitedCode
				user.InvitedUserId = invitationUser.ID
			}
		}

		user.Mobile = oReq.Mobile
		user.LastLoginIp = clientIp
		user.LastLoginTime = time.Now()
		user.AccessToken = utils.GetUUID()

		var userSystem model.UserEnv
		if err := utils.GetStructFromJson(&userSystem, oReq.UserEnv); err != nil {
			logger.Error(err)
			user.RegEnv = oReq.UserEnv
		} else {
			userSystem.Ip = utils.GetClientIp(c.Request.Header)
			jsonStr := utils.GetJsonFromStruct(userSystem)
			logger.Info("复制IP userSystem:", jsonStr)
			if jsonStr != "" {
				user.RegEnv = jsonStr
			}
		}

		if err = user.Save(); err != nil || user.ID == 0 {
			logger.Error(err)
			msg = "生成记录失败"
			return
		}
		logger.Info("新用户信息生成成功 userID:", user.ID)
		if user.ShortId == "" {
			logger.Info("开始创建用户短标识 userID:", user.ID)
			if err := user.SetShortId(); err != nil {
				msg = "创建用户存储路径失败"
				logger.Error(msg, err, "userId:", user.ID)
			}
			logger.Info("用户短标识创建完成 userID:", user.ID, "  ShortId:", user.ShortId, "  privateStorage:", user.PrivateStorage)
		}
		if user.ID > 0 && false {
			balance := model.AmountBalance{
				UserId:         user.ID,
				OperatorId:     user.ID,
				OccurredAmount: decimal.NewFromInt(5),
			}

			orderNo, err := model.OrderNo.NewByOrderType(enums.OrderTypeEnum.UserGift, 0)
			if err != nil {
				logger.Error(err)
				msg = "生成单号错误"
				return
			}

			if err = balance.GetBalanceObject(orderNo, 0, user.ID, enums.OrderTypeEnum.UserGift, balance.OccurredAmount, "新用户赠送", "注册自动添加", user.ID, "用户"); err != nil {
				logger.Error(err)
				msg = "生成流水数据错误"
				return
			}
			if err = model.Transactions.ManageAddAmount(&balance); err != nil {
				logger.Error(err)
				msg = "金额添加出错"
				return
			}

		}

	} else {
		//if err := user.SetLastLogin(clientIp); err != nil {
		//	logger.Error("设置最后登录信息失败", err)
		//}
	}

	if user.ID == 0 {
		msg = "生成用户信息失败"
		logger.Error(msg, oReq)
		return
	}

	if user.Status != 1 {
		msg = "账号不可用"
		logger.Error(msg, "userId:", user.ID)
		return
	}

	if oReq.Ticket != "" {
		postUrl := "http://*************:5102/api/v1/notify/wechat/official/bind_mobile"
		postData := make(map[string]interface{})
		postData["ticket"] = oReq.Ticket
		postData["mobile"] = user.Mobile
		if ginH, err := service.PostNodeForGin(0, postUrl, postData); err != nil {
			msg = err.Error()
			logger.Error("绑定手机mobile:", user.Mobile, " ginH:", utils.GetJsonFromStruct(ginH))
		} else {
			//c.JSON(http.StatusOK, ginH)
			//code = 0
			logger.Info("绑定手机mobile:", user.Mobile, " ginH:", utils.GetJsonFromStruct(ginH))
			if tmpH, err := service.ResultGinH(ginH); err != nil {
				msg = "解析出错"
				logger.Error("绑定手机mobile:", user.Mobile, " ginH:", utils.GetJsonFromStruct(ginH))
			} else {
				if tmpH.Code == 0 {
					if val, ok := tmpH.Result["account_id"]; ok {
						accountId := uint(val.(float64))
						if accountId > 0 {
							if err := user.SetAccountId(accountId); err != nil {
								logger.Error("绑定手机mobile:", user.Mobile, " ginH:", utils.GetJsonFromStruct(ginH))
							} else {
								logger.Info("绑定手机mobile:", user.Mobile, " 账号ID设置成功 ")
							}
						}
					}
				}
			}
		}
	}

	if user.AccessToken == "" {
		user.AccessToken = utils.GetUUID()
		if err := user.SetAccessToken(user.AccessToken); err != nil {
			msg = "设置Access失败"
			logger.Error(msg, err)
			return
		}
	}

	if token, err := middleware.NewJWT().SetToken(user.ID, user.Username, user.Mobile, user.DisplayName, user.Role, user.AccessToken, 0); err != nil {
		logger.Error(err)
		msg = "生成Token失败"
		return
	} else {
		result["token"] = token
	}

	resp := UserInfoResp{
		UserId:      user.ID,
		Username:    user.Username,
		DisplayName: user.DisplayName,
		Mobile:      utils.FormatMobileStar(user.Mobile),
	}
	result["user"] = resp
	msg = "登录成功"
	code = 0

	go func() {
		if err := user.SetLastLogin(clientIp); err != nil {
			logger.Error("设置最后登录信息失败", err)
		}
		if _, err := service.LocationService.PushJson("user", user.ID); err != nil {
			logger.Error(err)
		}
	}()

}

func GetToken(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	//if config.Env != enums.EnvEnum.DEV && config.Env != enums.EnvEnum.TEST {
	//	msg = "环境错误"
	//	return
	//}

	var oReq getTokenReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "无效的参数"
		return
	}

	if oReq.Pw != common.Cipher {
		msg = "口令不正确"
		return
	}

	var user model.User
	if err := user.GetByMobile(oReq.Mobile); err != nil {
		msg = "查询用户信息失败"
		logger.Error(msg, err)
		return
	}
	if token, err := middleware.NewJWT().SetToken(user.ID, user.Username, user.Mobile, user.DisplayName, user.Role, user.AccessToken, 0); err != nil {
		logger.Error(err)
		msg = "生成Token失败"
		return
	} else {
		result["token"] = token
	}

	if user.AccessToken == "" {
		user.AccessToken = utils.GetUUID()
		if err := user.SetAccessToken(user.AccessToken); err != nil {
			msg = "设置Access失败"
			logger.Error(msg, err)
			return
		}
	}

	resp := UserInfoResp{
		UserId:      user.ID,
		Username:    user.Username,
		DisplayName: user.DisplayName,
		Mobile:      utils.FormatMobileStar(user.Mobile),
	}
	result["user"] = resp
	msg = "登录成功"
	code = 0
}

func Login(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq loginReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "无效的参数"
		return
	}
	if oReq.Username == "" || oReq.Password == "" {
		msg = "参数错误"
		return
	}
	if len(oReq.Username) < 2 {
		msg = "用户名不正确"
		return
	}

	if len(oReq.Password) < 5 {
		msg = "密码不正确"
		return
	}

	var user model.User
	if utils.IsMobile(oReq.Username) {
		if err := user.GetByMobile(oReq.Username); err != nil {
			msg = "查询失败"
			logger.Error(msg, err)
			return
		}
	} else {
		if err := user.GetByUsername(oReq.Username); err != nil {
			msg = "查询账号失败"
			logger.Error(msg, err)
			return
		}
	}

	if user.ID == 0 {
		msg = "生成用户信息失败"
		logger.Error(msg, utils.GetJsonFromStruct(oReq))
		return
	}

	if user.Status != 1 {
		msg = "账号不可用"
		logger.Error(msg, "userId:", user.ID)
		return
	}

	if user.AccessToken == "" {
		user.AccessToken = utils.GetUUID()
		if err := user.SetAccessToken(user.AccessToken); err != nil {
			msg = "设置Access失败"
			logger.Error(msg, err)
			return
		}
	}
	if err := user.CheckPassword(oReq.Password); err != nil {
		msg = "密码不正确"
		logger.Error(msg, err)
		return
	}

	//$2a$10$MSE1zUFM10FCq6lJA1H6OuJTXolsL.USJeFScLnM0i7BO/zqpQi3a

	if token, err := middleware.NewJWT().SetToken(user.ID, user.Username, user.Mobile, user.DisplayName, user.Role, user.AccessToken, 0); err != nil {
		logger.Error(err)
		msg = "生成Token失败"
		return
	} else {
		result["token"] = token
	}

	resp := UserInfoResp{
		UserId:      user.ID,
		Username:    user.Username,
		DisplayName: user.DisplayName,
		Mobile:      utils.FormatMobileStar(user.Mobile),
	}
	result["user"] = resp
	msg = "登录成功"
	code = 0

	go func() {
		clientIp := utils.GetClientIp(c.Request.Header)
		if err := user.SetLastLogin(clientIp); err != nil {
			logger.Error("设置最后登录信息失败", err)
		}
		if _, err := service.LocationService.PushJson("user", user.ID); err != nil {
			logger.Error(err)
		}
	}()
}

// setup session & cookies and then return user info
func setupLogin(user *model.User, c *gin.Context) {
	session := sessions.Default(c)

	claims := middleware.MyClaims{
		UserId:      user.ID,
		Username:    user.Username,
		DisplayName: user.DisplayName,
		Mobile:      user.Mobile,
		Role:        user.Role,
		AccessToken: user.AccessToken,
	}
	session.Set("claims", claims)
	err := session.Save()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "无法保存会话信息，请重试",
			"success": false,
		})
		return
	}
	cleanUser := model.User{
		Username:    user.Username,
		DisplayName: user.DisplayName,
		Role:        user.Role,
		Status:      user.Status,
	}
	cleanUser.ID = user.ID
	c.JSON(http.StatusOK, gin.H{
		"message": "",
		"success": true,
		"data":    cleanUser,
	})
}

func (obj userApi_) Info(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		authorization := c.Request.Header.Get("Authorization")
		if authorization != "" {
			c.SetCookie("Authorization", authorization, 7*86400, "/", ".chenyu.cn", true, true)
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var user model.User

	if err := user.GetById(claims.UserId); err != nil {
		logger.Error(err)
		msg = "用户参数错误"
	}

	validCardAmount := decimal.NewFromInt(-1)
	validCardCount := -1
	var card model.Card
	//if validCount, err := card.CardValidCount(claims.UserId); err != nil {
	//	logger.Error(err)
	//	validCardCount = -1
	//} else {
	//	validCardCount = validCount
	//}

	if validAmount, validCount, err := card.CardValid(claims.UserId, 0); err != nil {
		logger.Error(err)
	} else {
		validCardAmount = validAmount
		validCardCount = validCount
	}

	avatarUrl := ""
	if user.Avatar != "" {
		avatarUrl = config.DiffusionDomain + user.Avatar
	}

	userInfoResp := UserInfoResp{
		UserId:          user.ID,
		Username:        user.Username,
		DisplayName:     user.DisplayName,
		Mobile:          utils.FormatMobileStar(user.Mobile),
		CertName:        utils.FormatNameStar(config.DecryptAes(user.CertName)),
		CertNo:          utils.FormatNoStar(config.DecryptAes(user.CertNo)),
		Amount:          user.Amount,
		Reward:          user.RewardAmount.Sub(user.RewardFrozen),
		RewardAmount:    user.RewardAmount,
		RewardFrozen:    user.RewardFrozen,
		InvitationCode:  user.InvitationCode,
		ValidCardCount:  validCardCount,
		ValidCardAmount: validCardAmount,
		AvatarUrl:       avatarUrl,
		UserType:        user.UserType,
		Insider:         user.Insider,
		GuideStatus:     user.GuideStatus,
	}
	if user.StudentCerifyId > 0 {
		userInfoResp.Student = "t"
	}
	if user.CompanyCerifyId > 0 {
		userInfoResp.Company = "t"
	}
	if user.AccountId > 0 {
		userInfoResp.WeixinBind = "t"
	}

	result["user"] = userInfoResp
	code = 0
}

func Logout(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims != nil && claims.UserId > 0 {
		var user model.User
		if err := user.GetById(claims.UserId); err != nil {
			logger.Error(err)
		} else {
			if err := middleware.RemoveAccessToken(user.AccessToken); err != nil {
				logger.Error(err, " access_token:", user.AccessToken)
			}
			accessToken := utils.GetUUID()
			if accessToken != "" {
				if err := user.SetAccessToken(accessToken); err != nil {
					msg = "设置Access失败"
					logger.Error(msg, err)
					return
				}
			}
		}
	}

	//session := sessions.Default(c)
	//session.Clear()
	//if err := session.Save(); err != nil {
	//	msg = "退出失败"
	//	logger.Error(msg, err)
	//	return
	//}
	//c.SetCookie("Authorization", "", -1, "/", "", true, true)
	c.SetCookie("Authorization", "", 7*86400, "/", ".chenyu.cn", true, true)

	code = 0
	msg = "退出成功"
}

func Destroy(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	session := sessions.Default(c)
	session.Clear()
	if err := session.Save(); err != nil {
		msg = "退出失败"
		logger.Error(msg, err)
		return
	}
	code = 0
	msg = "退出成功"
}

func Register(c *gin.Context) {
	if !common.RegisterEnabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "管理员关闭了新用户注册",
			"success": false,
		})
		return
	}
	if !common.PasswordRegisterEnabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "管理员关闭了通过密码进行注册，请使用第三方账户验证的形式进行注册",
			"success": false,
		})
		return
	}
	var user model.User
	err := json.NewDecoder(c.Request.Body).Decode(&user)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}
	if err := common.Validate.Struct(&user); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "输入不合法 " + err.Error(),
		})
		return
	}
	//if common.EmailVerificationEnabled {
	//	if user.Email == "" || user.VerificationCode == "" {
	//		c.JSON(http.StatusOK, gin.H{
	//			"success": false,
	//			"message": "管理员开启了邮箱验证，请输入邮箱地址和验证码",
	//		})
	//		return
	//	}
	//	if !common.VerifyCodeWithKey(user.Email, user.VerificationCode, common.EmailVerificationPurpose) {
	//		c.JSON(http.StatusOK, gin.H{
	//			"success": false,
	//			"message": "验证码错误或已过期",
	//		})
	//		return
	//	}
	//}
	//affCode := user.AffCode // this code is the inviter's code, not the user's own code
	//inviterId, _ := model.GetUserIdByAffCode(affCode)
	//cleanUser := model.User{
	//	Username:    user.Username,
	//	Password:    user.Password,
	//	DisplayName: user.Username,
	//	InviterId:   inviterId,
	//}
	//if common.EmailVerificationEnabled {
	//	cleanUser.Email = user.Email
	//}
	//if err := cleanUser.Insert(inviterId); err != nil {
	//	c.JSON(http.StatusOK, gin.H{
	//		"success": false,
	//		"message": err.Error(),
	//	})
	//	return
	//}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func (obj userApi_) ChangePasswordByMobile(c *gin.Context) {

	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	var oReq resetPasswordReq

	if err := c.ShouldBindBodyWith(&oReq, binding.JSON); err != nil {
		msg = "获取数据失败"
		return
	}

	//if !utils.IsMobile(oReq.Mobile) {
	//	msg = "输入手机号码不正确"
	//	return
	//}

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		msg = "请先登录"
		return
	}
	//var user model.User
	//if err := user.GetById(claims.UserId); err != nil {
	//	msg = "查询数据失败"
	//	return
	//}
	//if err := user.GetByMobile(oReq.Mobile); err != nil {
	//	msg = "查询数据失败"
	//	return
	//}
	//if user.Mobile != oReq.Mobile {
	//	msg = "手机号码跟登录账号不一致"
	//	return
	//}
	//c.Set("claims", &claims)
	obj.ChangePassword(c)
	code = 0
}

func (obj userApi_) ChangePassword(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		msg = "请先登录"
		return
	}

	debug := false
	var oReq resetPasswordReq
	err := c.ShouldBindBodyWith(&oReq, binding.JSON)
	if err != nil {
		msg = "数据获取失败"
		return
	}

	if len(oReq.SmsCode) != 4 {
		msg = "短信验证码不正确"
		return
	}

	if len(oReq.Password) < 5 {
		msg = "密码不能小于5位"
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		logger.Error(err)
		msg = "获取用户信息失败"
		return
	}

	redisKey := enums.RedisKeyEnum.SmsModifyPassword + user.Mobile
	testCount, _ := common.RedisGet(redisKey + ":testcount")
	if debug {
		testCount = "2"
	}
	if testCount == "" {
		msg = "验证码已失效，请10分钟后重试"
		return
	}
	iTestCount, _ := strconv.Atoi(testCount)
	if iTestCount > 10 {
		msg = "验证码尝试次数过多，请10分钟后重试"
		return
	}
	iTestCount = iTestCount + 1
	if err := common.RedisSet(redisKey+":testcount", strconv.Itoa(iTestCount), time.Minute*10); err != nil {
		msg = "设置短信验证码尝试参数失败"
		return
	}

	v, _ := common.RedisGet(redisKey)
	if debug {
		v = "1234"
	}
	if len(v) == 0 {
		msg = "验证码已失效，请重试"
		return
	}
	if v != oReq.SmsCode {
		msg = "短信验证码不正确"
		return
	}

	user.Password = oReq.Password
	if err := user.SetPassword(); err != nil {
		logger.Error(err)
		msg = "设置密码失败"
		return
	}
	msg = "密码修改成功"
	code = 0
}
func (obj userApi_) SetUserInfo(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq setUserInfoReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Action == "" {
		msg = "操作参数错误"
		logger.Error(msg, oReq, claims.UserId)
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		logger.Error(err)
		msg = "用户参数错误"
	}
	if oReq.Action == "SetGuideStatus" {
		if oReq.GuideId <= 0 {
			//1：应用商店指引
			//2：算力市场指引
			msg = "参数错误"
			return
		}
		if !guide.IsEventId(oReq.GuideId) {
			msg = "指引ID不正确"
			return
		}
		guideStatus := guide.SetEventCompleted(user.GuideStatus, oReq.GuideId)
		if err := user.SetGuideStatus(guideStatus); err != nil {
			msg = "设置新手指引失败"
			return
		}
		code = 0
		msg = "新手指引设置成功"
		return
	} else if oReq.Action == "SetDisplayName" {
		if len(oReq.DisplayName) < 2 {
			msg = "昵称长度需要大于两个字符"
			logger.Error(msg, oReq, claims.UserId)
			return
		}
		if len(oReq.DisplayName) > 50 {
			oReq.DisplayName = oReq.DisplayName[:50]
		}
		var tmpUser model.User
		if err := tmpUser.GetByDisplayName(oReq.DisplayName); err != nil {
			if err == gorm.ErrRecordNotFound {

			} else {
				msg = "查询出错"
				logger.Error(msg, err, claims.UserId)
				return
			}
		} else {
			msg = "昵称已存储，请更换昵称"
			return
		}
		if err := user.SetDisplayName(oReq.DisplayName); err != nil {
			msg = "设置昵称出错"
			logger.Error(msg, err, claims.UserId)
			return
		}
		code = 0
		msg = "昵称设置成功"
		return
	} else if oReq.Action == "SetAvatar" {
		if oReq.AvatarBase64 == "" {
			msg = "请上传头像"
			logger.Error(msg, oReq, claims.UserId)
			return
		}
		oMd5Str := fmt.Sprintf("%d,%s", user.ID, user.CreatedAt.Format("2006-01-02 15:04:05.000"))
		path0 := user.ID / 10000
		path := "cpn/user_avatar/" + shortid.DecimalToBase36(int64(path0)) + "/" + utils.GetMd5(oMd5Str) + ".jpg"

		absolutePath := config.DiffusionFilePath + path
		directory := filepath.Dir(absolutePath) // 获取目录路径
		// 创建目录，存在则不创建，不存在则创建
		if err := os.MkdirAll(directory, 0755); err != nil {
			msg = "目录创建失败"
			logger.Error(msg, err)
			return
		}

		if img, err := myimg.Base64ToImg(oReq.AvatarBase64); err != nil {
			//logger.Error(err, oReq.CoverBase64)
			msg = "base64转图片失败"
			logger.Error(msg, err)
			return
		} else {
			logger.Info(user.ID, "   x:", img.Bounds().Size().X, "   y:", img.Bounds().Size().Y)
			small := myimg.ResizeImg(180, 180, img, true)
			if err := myimg.ImgToFile(small, absolutePath); err != nil {
				msg = "缩略图保存失败"
				logger.Error(msg, err)
			} else {
				logger.Info(user.ID, "  缩略图生成成功")
			}
		}

		if user.Avatar == path {
			logger.Info(user.ID, "  logo路径一样，不更新")
		} else {
			if err := user.SetAvatar(path); err != nil {
				msg = "设置Logo信息失败"
				logger.Error(msg, user.ID, err)
				return
			} else {
				logger.Info(user.ID, "  Logo设置成功")
			}
		}
		code = 0
		msg = "设置成功"
		return
	} else if oReq.Action == "SetMobile" {
		if utils.IsMobile(oReq.Mobile) == false {
			msg = "手机号码不正确"
			logger.Error(msg, oReq, claims.UserId)
			return
		}

		if len(oReq.SmsCode) != 4 {
			msg = "短信验证码不正确"
			return
		}

		redisKey := enums.RedisKeyEnum.SmsLogin + oReq.Mobile
		//common.RedisSet(redisKey+":testcount", strconv.Itoa(1), time.Minute*10) //测试

		testCount, _ := common.RedisGet(redisKey + ":testcount")
		if testCount == "" {
			msg = "验证码已失效，请10分钟后重试"
			logger.Error(msg, oReq.Mobile)
			return
		}
		iTestCount, _ := strconv.Atoi(testCount)
		if iTestCount > 10 {
			msg = "验证码尝试次数过多，请10分钟后重试"
			logger.Error(msg, oReq.Mobile)
			return
		}
		iTestCount = iTestCount + 1
		if err := common.RedisSet(redisKey+":testcount", strconv.Itoa(iTestCount), time.Minute*10); err != nil {
			msg = "设置短信验证码尝试次数失败"
			logger.Error(msg, oReq.Mobile)
			return
		}

		//common.RedisSet(enums.RedisKeyEnum.SmsLogin+loginReq.Mobile, "1234", time.Minute*10) //测试
		v, _ := common.RedisGet(enums.RedisKeyEnum.SmsLogin + oReq.Mobile)

		if len(v) == 0 {
			msg = "验证码已失效，请重试"
			logger.Error(msg)
			return
		}
		if v != oReq.SmsCode {
			msg = "短信验证码不正确"
			logger.Error(msg, oReq, claims.UserId)
			return
		}

		var tmpUser model.User
		if err := tmpUser.GetByMobile(oReq.Mobile); err != nil {
			if err != gorm.ErrRecordNotFound {
				msg = "查询手机号码出错"
				logger.Error(msg, err, oReq, claims.UserId)
				return
			}
		}
		if tmpUser.ID > 0 {
			msg = "该手机号码已经注册"
			logger.Error(msg, oReq, claims.UserId)
			return
		}
		if err := user.SetMobile(oReq.Mobile); err != nil {
			msg = "重置手机号码出错"
			logger.Error(msg, err, oReq, claims.UserId)
			return
		}
		msg = "手机号码更换成功"
		code = 0
		return
	} else {
		msg = "参数错误"
		return
	}
}
