package manage

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net"
	"net/http"
	"runtime"
	"strings"
	"time"
)

type virtualApi_ struct {
}

var VirtualApi virtualApi_

type virtualAddReq struct {
	Host          string  `json:"host"`
	GpuModelId    uint    `json:"gpu_model_id"`
	NodeId        uint    `json:"node_id"`
	ContainerCpus float64 `json:"container_cpus"`
	ContainerMem  string  `json:"container_mem"`
}

type virtualSetReq struct {
	Action      string `json:"action"`
	VirtualId   uint   `json:"virtual_id"`
	ContainerID string `json:"container_id"`
	StartupMark string `json:"startup_mark"`
	KeepSeconds int64  `json:"keep_seconds"`
}

type virtualReq struct {
	VirtualId   uint   `json:"virtual_id"`
	VirtualUuid string `json:"virtual_uuid"`
	GpuModelId  uint   `json:"gpu_model_id"`
	NodeId      uint   `json:"node_id"`
	Status      int    `json:"status"`
	Remark      string `json:"remark"`
	PodIds      string `json:"pod_ids"`
	Operator    string `json:"operator"`
	Page        int    `json:"page"`
	PageSize    int    `json:"page_size"`
}

type removeImageReq struct {
	VirtualId   uint   `json:"virtual_id"`
	ImageSha256 string `json:"image_sha256"`
	WithA       bool   `json:"with_a"`
}

type removeDockerReq struct {
	VirtualId   uint   `json:"virtual_id"`
	StartupMark string `json:"startup_mark"`
	DockerId    string `json:"docker_id"`
}

type monitorPrometheusReq struct {
	VirtualId uint   `json:"virtual_id"`
	StartTime int64  `json:"start_time"`
	EndTime   int64  `json:"end_time"`
	Step      int64  `json:"step"` // 时间间隔
	Metrics   string `json:"metrics"`
}

type runCommandReq struct {
	NodeId       uint   `json:"node_id"`
	PodId        uint   `json:"pod_id"`
	ImageId      uint   `json:"image_id"`
	NeedGpus     int    `json:"need_gpus"`
	UserDataPath string `json:"user_data_path"`
}

type pullImageReq struct {
	VirtualId uint `json:"virtual_id"`
	ImageId   uint `json:"image_id"`
}

type StatusLogResp struct {
	Operator   string            `json:"operator"`
	Remark     string            `json:"remark"`
	TxtStatus  string            `json:"txt_status,omitempty"`
	LastStatus int               `json:"last_status"`
	CurStatus  int               `json:"cur_status"`
	CreateAt   jsontime.JsonTime `json:"create_at"`
}

type virtualResp struct {
	ID            uint              `json:"id"`
	NodeId        string            `json:"node_id"`
	Region        int               `json:"region"`
	Host          string            `json:"host"`
	Port          int               `json:"port"`
	FreeGpus      int               `json:"free_gpus"`
	TotalGpus     int               `json:"total_gpus"`
	TotalInstance int               `json:"total_instance"`
	GpuModelId    uint              `json:"gpu_model_id"`
	GpuModelName  string            `json:"gpu_model_name"`
	GpuModelTitle string            `json:"gpu_model_title"`
	Status        int               `json:"status"`
	StatusTxt     string            `json:"status_txt"`
	Remark        string            `json:"remark"`
	DockerAt      int64             `json:"docker_at"`
	TimeoutAt     int64             `json:"timeout_at"`
	InitedAt      int64             `json:"inited_at"`
	PodIds        string            `json:"pod_ids"`
	ImageIds      string            `json:"image_ids"`
	DockerAtTime  jsontime.JsonTime `json:"docker_at_time"`
	TimeoutAtTime jsontime.JsonTime `json:"timeout_at_time"`
	InitedAtTime  jsontime.JsonTime `json:"inited_at_time"`
	LastCheckTime jsontime.JsonTime `json:"last_check_time"`
	CreatedAt     jsontime.JsonTime `json:"created_at"`
}

func (obj virtualApi_) Add(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		msg = "权限不足"
		return
	}

	var oReq virtualAddReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if net.ParseIP(oReq.Host) == nil {
		msg = "Ip格式不正确"
		return
	}

	if oReq.ContainerMem == "" {
		oReq.ContainerMem = "60g"
	}
	if strings.HasSuffix(oReq.ContainerMem, "g") || strings.HasSuffix(oReq.ContainerMem, "m") {
		str := strings.ReplaceAll(oReq.ContainerMem, "g", "")
		str = strings.ReplaceAll(str, "m", "")
		if val := utils.String2Int(str); val == 0 {
			msg = "分配内存不正确"
			return
		}
	} else {
		msg = "分配内存设置不正确"
		return
	}

	if oReq.ContainerCpus == 0 {
		oReq.ContainerCpus = 10
	}

	if !service.ValidContainerMemory(oReq.ContainerMem) {
		msg = "容器分配内存不正确"
		return
	}

	if oReq.ContainerCpus <= 0 {
		msg = "请设置容器分配Cpu"
		return
	}

	//{
	//	str := fmt.Sprintf("%f", oReq.ContainerCpus)
	//	ary := strings.Split(str, ".")
	//	if len(ary) >= 2 && len(ary[1]) > 1 {
	//		msg = "设置容器分配Cpu不正确，最多保留一位小数"
	//		return
	//	}
	//}

	var gpuModel model.GpuModel
	if err := gpuModel.GetById(oReq.GpuModelId); err != nil {
		msg = "查询Gpu型号失败"
		if err == gorm.ErrRecordNotFound {
			msg = "Gpu型号不存在"
		}
		logger.Error(msg, err)
		return
	}

	var virtual model.Virtual
	if err := virtual.GetByHost(oReq.Host); err != nil {
		if err == gorm.ErrRecordNotFound {

		} else {
			msg = "检测Host失败"
			logger.Error(msg, err)
			return
		}
	} else {
		msg = "该IP已经存在"
		return
	}
	virtual = model.Virtual{
		Uuid:          utils.GetUUID(),
		Status:        1,
		NodeId:        oReq.NodeId,
		Region:        1,
		Host:          oReq.Host,
		Port:          22,
		SshUser:       "root",
		SshPassword:   "chenyuworker!@#$",
		GpuModelId:    gpuModel.ID,
		ContainerCpus: oReq.ContainerCpus,
		ContainerMem:  oReq.ContainerMem,
	}
	if err := virtual.Save(); err != nil {
		msg = "添加失败"
		logger.Error(msg, err)
		return
	}
	code = 0
	msg = "添加成功"
}

func (obj virtualApi_) ActionFromNode(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq virtualSetReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	postData := make(map[string]interface{})
	postData["action"] = oReq.Action
	postData["virtual_id"] = oReq.VirtualId
	postData["container_id"] = oReq.ContainerID
	postData["startup_mark"] = oReq.StartupMark
	postData["keep_seconds"] = oReq.KeepSeconds

	if ginH, err := service.NodeService.ActionVirtual(oReq.VirtualId, postData); err != nil {
		msg = "设置失败"
		logger.Error(err)
		result["err"] = err.Error()
		return
	} else {
		code = 0
		c.JSON(http.StatusOK, ginH)
	}
}

func (obj virtualApi_) DetailFromNode(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq virtualReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if ginH, err := service.NodeService.GetVirtualDetail(oReq.VirtualId); err != nil {
		msg = "从节点获取信息失败"
		logger.Error(err)
		result["err"] = err.Error()
		return
	} else {
		code = 0
		c.JSON(http.StatusOK, ginH)
	}
}

func (obj virtualApi_) ContainerInfo(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		msg = "权限不足"
		return
	}

	var oReq virtualSetReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if container, err := service.NodeService.ContainerInfo(oReq.StartupMark); err != nil {
		msg = "获取容器信息失败 " + err.Error()
		logger.Error(msg, err, oReq.StartupMark)
		return
	} else {
		if container.SizeRw > 20*common.SizeB2G {
			msg = "容器系统盘新增内容已经大于20G，请减少后再关机或者直接销毁"
		}
		result["container"] = container
		code = 0
	}
}

func (obj virtualApi_) LocalImages(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq virtualReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if ginH, err := service.NodeService.GetVirtualLocalImages(oReq.VirtualId); err != nil {
		msg = "从节点获取信息失败"
		logger.Error(err)
		result["err"] = err.Error()
		return
	} else {
		code = 0
		c.JSON(http.StatusOK, ginH)
	}
}

func (obj virtualApi_) RemoveImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		msg = "权限不足"
		return
	}

	var oReq removeImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.VirtualId <= 0 {
		msg = "请输入虚拟机"
		return
	}
	if oReq.ImageSha256 == "" {
		msg = "请输入镜像ID"
		return
	}

	if ginH, err := service.NodeService.RemoveVirtualLocalImage(oReq.VirtualId, oReq.ImageSha256); err != nil {
		msg = "从节点获取信息失败"
		logger.Error(err)
		result["err"] = err.Error()
		return
	} else {
		code = 0
		c.JSON(http.StatusOK, ginH)
	}
}

func (obj virtualApi_) PruneImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		msg = "权限不足"
		return
	}

	var oReq removeImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.VirtualId <= 0 {
		msg = "请输入虚拟机"
		return
	}

	if ginH, err := service.NodeService.PruneVirtualLocalImage(oReq.VirtualId, oReq.WithA); err != nil {
		msg = "从节点获取信息失败"
		logger.Error(err)
		result["err"] = err.Error()
		return
	} else {
		code = 0
		c.JSON(http.StatusOK, ginH)
	}
}

func (obj virtualApi_) RefreshImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		msg = "权限不足"
		return
	}

	var oReq removeImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.VirtualId <= 0 {
		msg = "请输入虚拟机"
		return
	}

	if ginH, err := service.NodeService.RefreshVirtualLocalImage(oReq.VirtualId); err != nil {
		msg = "从节点获取信息失败"
		logger.Error(err)
		result["err"] = err.Error()
		return
	} else {
		code = 0
		c.JSON(http.StatusOK, ginH)
	}
}

func (obj virtualApi_) SetStatus(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		logger.Error("权限不足 claims:", utils.GetJsonFromStruct(claims))
		msg = "权限不足"
		return
	}

	var oReq virtualReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Remark == "" {
		msg = "请输入备注信息"
		return
	}
	if oReq.Operator == "" {
		msg = "请输入操作员"
	}

	var virtual model.Virtual
	if err := virtual.GetById(oReq.VirtualId); err != nil {
		msg = "获取虚拟机记录失败"
		logger.Error(err)
		return
	}

	logStruct := StatusLogResp{
		Operator:   oReq.Operator,
		Remark:     oReq.Remark,
		LastStatus: virtual.Status,
		CurStatus:  oReq.Status,
		CreateAt:   jsontime.Now(),
	}

	logJson := utils.GetJsonFromStruct(logStruct)
	operationLog := model.OperationLog{
		OperatorUserId: claims.UserId,
		LogType:        enums.OperationLogTypeEnum.VirutalStatus,
		OrigWhere:      enums.OperationOrigWhereEnum.Virtual,
		OrigId:         virtual.ID,
		Ip:             utils.GetClientIp(c.Request.Header),
		LogJson:        logJson,
		UserEnv:        "{}",
	}
	if err := operationLog.Save(); err != nil {
		msg = "保存日志失败"
		logger.Error(msg, err)
		return
	}

	if oReq.Status == 9 {
		if virtual.TotalInstance > 0 {
			msg = "该机器有正在运行的实例，请先关闭实例"
			return
		}
		if err := virtual.Delete(); err != nil {
			msg = err.Error()
			return
		}
		msg = "删除成功"
	} else {
		if virtual.Status == oReq.Status {
			if err := virtual.SetRemark(oReq.Remark); err != nil {
				msg = err.Error()
				return
			}
			msg = "状态一样，备注成功"
			return
		} else {
			if err := virtual.SetStatusAndRemark(oReq.Status, oReq.Remark); err != nil {
				msg = err.Error()
				return
			}
			if ginH, err := service.NodeService.SetVirtualStatus(oReq.VirtualId); err != nil {
				msg = err.Error()
				return
			} else {
				code = 0
				c.JSON(http.StatusOK, ginH)
			}
		}
	}
}

func (obj virtualApi_) SetGpuModel(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		logger.Error("权限不足 claims:", utils.GetJsonFromStruct(claims))
		msg = "权限不足"
		return
	}

	var oReq virtualReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.GpuModelId <= 0 {
		msg = "请选择显卡型号"
		return
	}
	if oReq.Operator == "" {
		msg = "请输入操作员"
		return
	}

	var virtual model.Virtual
	if err := virtual.GetById(oReq.VirtualId); err != nil {
		msg = "获取虚拟机记录失败"
		logger.Error(err)
		return
	}
	if oReq.GpuModelId == virtual.GpuModelId {
		msg = "要切换的显卡型号跟当前一致，无需切换"
		return
	}
	var curModel model.GpuModel
	var targetModel model.GpuModel
	if err := curModel.GetById(virtual.GpuModelId); err != nil {
		logger.Error(err)
	}
	if err := targetModel.GetById(oReq.GpuModelId); err != nil {
		logger.Error(err)
		msg = "查询显卡型号失败"
		return
	}

	remark := fmt.Sprintf("将显卡型号%s(%d)切换成%s(%d)", curModel.Title, virtual.GpuModelId, targetModel.Title, oReq.GpuModelId)

	logStruct := StatusLogResp{
		Operator:   oReq.Operator,
		Remark:     remark,
		LastStatus: virtual.Status,
		CurStatus:  oReq.Status,
		CreateAt:   jsontime.Now(),
	}

	logJson := utils.GetJsonFromStruct(logStruct)
	operationLog := model.OperationLog{
		OperatorUserId: claims.UserId,
		LogType:        enums.OperationLogTypeEnum.VirutalStatus,
		OrigWhere:      enums.OperationOrigWhereEnum.Virtual,
		OrigId:         virtual.ID,
		Ip:             utils.GetClientIp(c.Request.Header),
		LogJson:        logJson,
		UserEnv:        "{}",
	}
	if err := operationLog.Save(); err != nil {
		msg = "保存日志失败"
		logger.Error(msg, err)
		return
	}

	if oReq.Status != 0 {
		msg = "该机器不在下架状态，不能切换显卡类型"
		return
	}
	if virtual.TotalInstance > 0 {
		msg = "该机器有正在运行的实例，请先关闭实例"
		return
	}

	if err := virtual.SetGpuModelAndRemark(oReq.GpuModelId, remark); err != nil {
		msg = err.Error()
		return
	} else {
		msg = "显卡型号切换成功"
		code = 0
	}
}

func (obj virtualApi_) SetNode(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		logger.Error("权限不足 claims:", utils.GetJsonFromStruct(claims))
		msg = "权限不足"
		return
	}

	var oReq virtualReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.NodeId <= 0 {
		msg = "请选择节点"
		return
	}
	if oReq.Operator == "" {
		msg = "请输入操作员"
		return
	}

	var virtual model.Virtual
	if err := virtual.GetById(oReq.VirtualId); err != nil {
		msg = "获取虚拟机记录失败"
		logger.Error(err)
		return
	}
	if oReq.NodeId == virtual.NodeId {
		msg = "要切换的节点跟当前一致，无需切换"
		return
	}
	var curNode model.Node
	var targetNode model.Node
	if err := curNode.GetById(virtual.NodeId); err != nil {
		logger.Error(err)
	}
	if err := targetNode.GetById(oReq.NodeId); err != nil {
		logger.Error(err)
		msg = "查询节点信息失败"
		return
	}

	remark := fmt.Sprintf("将节点%s(%d)切换成%s(%d)", curNode.Title, virtual.NodeId, targetNode.Title, oReq.NodeId)

	logStruct := StatusLogResp{
		Operator:   oReq.Operator,
		Remark:     remark,
		LastStatus: virtual.Status,
		CurStatus:  oReq.Status,
		CreateAt:   jsontime.Now(),
	}

	logJson := utils.GetJsonFromStruct(logStruct)
	operationLog := model.OperationLog{
		OperatorUserId: claims.UserId,
		LogType:        enums.OperationLogTypeEnum.VirutalStatus,
		OrigWhere:      enums.OperationOrigWhereEnum.Virtual,
		OrigId:         virtual.ID,
		Ip:             utils.GetClientIp(c.Request.Header),
		LogJson:        logJson,
		UserEnv:        "{}",
	}
	if err := operationLog.Save(); err != nil {
		msg = "保存日志失败"
		logger.Error(msg, err)
		return
	}

	if oReq.Status != 0 {
		msg = "该机器不在下架状态，不能切换节点"
		return
	}
	if virtual.TotalInstance > 0 {
		msg = "该机器有正在运行的实例，请先关闭实例"
		return
	}

	if err := virtual.SetNodeAndRemark(oReq.NodeId, remark); err != nil {
		msg = err.Error()
		return
	} else {
		msg = "节点切换成功"
		code = 0
	}
}

func (obj virtualApi_) SetPodIds(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		logger.Error("权限不足 claims:", utils.GetJsonFromStruct(claims))
		msg = "权限不足"
		return
	}

	var oReq virtualReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.PodIds != "" {
		if strings.HasPrefix(oReq.PodIds, "|") && strings.HasSuffix(oReq.PodIds, "|") {

		} else {
			msg = "定向参数格式不正确"
			return
		}

	}

	var virtual model.Virtual
	if err := virtual.GetById(oReq.VirtualId); err != nil {
		msg = "获取虚拟机记录失败"
		logger.Error(err)
		return
	}
	logMap := make(map[string]interface{})
	logMap["old_pod_ids"] = virtual.PodIds

	logJson := utils.GetJsonFromStruct(logMap)
	operationLog := model.OperationLog{
		OperatorUserId: claims.UserId,
		LogType:        enums.OperationLogTypeEnum.VirutalPodIds,
		OrigWhere:      enums.OperationOrigWhereEnum.Virtual,
		OrigId:         virtual.ID,
		Ip:             utils.GetClientIp(c.Request.Header),
		LogJson:        logJson,
		UserEnv:        "{}",
	}
	if err := operationLog.Save(); err != nil {
		msg = "保存日志失败"
		logger.Error(msg, err)
		return
	}

	if err := virtual.SetPodIds(oReq.PodIds); err != nil {
		msg = err.Error()
		return
	}
	msg = "设置成功"
	code = 0
	return
}

func (obj virtualApi_) StatusLog(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq virtualReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 1000 {
		oReq.PageSize = 20
	}

	var aryStatusLog = make([]StatusLogResp, 0)
	var ary = make([]model.OperationLog, 0)
	var operationLog model.OperationLog
	if total, err := operationLog.GetList(&ary, enums.OperationLogTypeEnum.VirutalStatus, enums.OperationOrigWhereEnum.Virtual, oReq.VirtualId, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(oReq))
		return
	} else {
		for _, item := range ary {
			var op StatusLogResp
			if err := utils.GetStructFromJson(&op, item.LogJson); err != nil {
				logger.Error(err, item.LogJson)
			} else {
				op.TxtStatus = fmt.Sprintf("状态由%s(%d)变更为%s(%d)", enums.VirtualStatusEnum.Name(op.LastStatus), op.LastStatus, enums.VirtualStatusEnum.Name(op.CurStatus), op.CurStatus)
				aryStatusLog = append(aryStatusLog, op)
			}
		}
		result["total"] = total
		result["items"] = aryStatusLog
		code = 0
	}
}

func (obj virtualApi_) RemoveDockerFromMemory(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		msg = "权限不足"
		return
	}

	var oReq removeDockerReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var virtual model.Virtual
	if err := virtual.GetById(oReq.VirtualId); err != nil {
		msg = "获取虚拟机记录失败"
		logger.Error(err)
		return
	}

	if ginH, err := service.NodeService.RemoveDocker(oReq.VirtualId, oReq.StartupMark, oReq.DockerId); err != nil {
		msg = err.Error()
		return
	} else {
		code = 0
		c.JSON(http.StatusOK, ginH)
	}

}

func (obj virtualApi_) Del(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	var oReq virtualReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var virtual model.Virtual
	if err := virtual.GetById(oReq.VirtualId); err != nil {
		msg = "获取虚拟机记录失败"
		logger.Error(err)
		return
	}
	if err := virtual.Delete(); err != nil {
		msg = err.Error()
		return
	}
	code = 0
	msg = "设置成功"
}

func (obj virtualApi_) SpellRunCommandFromNode(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code == 1 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq runCommandReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.UserDataPath == "" {
		oReq.UserDataPath = "usrtst"
	}

	var pod model.Pod
	if err := pod.GetById(oReq.PodId); err != nil {
		msg = "Pod信息获取失败"
		logger.Error(err)
		return
	}

	imageId := oReq.ImageId
	if imageId == 0 && pod.ImageTags != "" {
		tmpAry := strings.Split(pod.ImageTags, " ")
		if len(tmpAry) > 0 {
			queryParm := make(map[string]interface{})
			queryParm["pod_id"] = oReq.PodId
			queryParm["image_tag"] = tmpAry[0]
			queryParm["image_type"] = enums.ImageTypeEnum.Public
			var aryPass = make([]model.PodImage, 0)
			var podImage model.PodImage
			if _, err := podImage.ListPro(&aryPass, queryParm, 1, 100); err != nil {
				msg = "镜像查询失败"
				logger.Error(msg, err)
				return
			} else {
				if len(aryPass) > 0 {
					imageId = aryPass[0].ID
				}
			}
		}
	}

	if ginH, err := service.NodeService.SpellRunCommand(oReq.NodeId, oReq.PodId, imageId, oReq.NeedGpus, oReq.UserDataPath); err != nil {
		msg = err.Error()
		logger.Error(msg, err)
		return
	} else {
		code = 0
		c.JSON(http.StatusOK, ginH)
	}
}

func (obj virtualApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	aryGpuModel := make([]model.GpuModel, 0)
	var gpuModel model.GpuModel
	if _, err := gpuModel.List(&aryGpuModel, 0, "", -1, 1, 2048); err != nil {
		logger.Error(err)
		//return
	}

	var virtual model.Virtual
	var ary = make([]virtualResp, 0)
	if total, err := virtual.List(&ary, 0, -1, 1, 1000); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {

			if ary[i].TimeoutAt > 0 {
				ary[i].TimeoutAtTime = jsontime.FromUnix(ary[i].TimeoutAt)
			}

			ary[i].StatusTxt = enums.VirtualStatusEnum.Name(ary[i].Status)

			for _, tmp := range aryGpuModel {
				if tmp.ID == ary[i].GpuModelId {
					ary[i].GpuModelName = tmp.GpuName
					ary[i].GpuModelTitle = tmp.Title
				}
			}
		}
		result["virtuals"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj virtualApi_) ListFromRedis(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var virtual model.Virtual

	var ary = make([]virtualResp, 0)
	if total, err := virtual.List(&ary, 0, -1, 1, 100); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {
			if ary[i].Status == 1 {
				ary[i].StatusTxt = "有效"
			} else {
				ary[i].StatusTxt = "无效"
			}
		}
		result["virtuals"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj virtualApi_) PullImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		msg = "权限不足"
		return
	}

	var oReq pullImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var virtual model.Virtual
	if err := virtual.GetById(oReq.VirtualId); err != nil {
		msg = "获取虚拟机记录失败"
		logger.Error(err)
		return
	}
	if ginH, err := service.NodeService.PullImage(virtual.NodeId, oReq.VirtualId, oReq.ImageId); err != nil {
		msg = err.Error()
		return
	} else {
		code = 0
		c.JSON(http.StatusOK, ginH)
	}
}

func (obj virtualApi_) MonitorData(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("StartupLoop panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		msg = "权限不足"
		return
	}

	var oReq monitorPrometheusReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var virtual model.Virtual
	if err := virtual.GetById(oReq.VirtualId); err != nil {
		msg = "获取虚拟机记录失败"
		logger.Error(err)
		return
	}
	if oReq.StartTime == 0 {
		msg = "开始时间不能为空"
		logger.Error("开始时间不能为空")
		return
	}
	if oReq.EndTime == 0 {
		logger.Info("结束时间为空，按照当前时间查询")
		oReq.EndTime = time.Now().UnixMilli()
	}

	if oReq.StartTime > oReq.EndTime {
		msg = "开始时间不能大于结束时间"
		logger.Error("开始时间不能大于结束时间")
		return
	}
	err, data := service.MonitorVirtual(virtual.Host, oReq.Metrics, oReq.StartTime, oReq.EndTime, oReq.Step)
	if err != nil {
		msg = err.Error()
		logger.Error(msg)
		return
	}
	result["data"] = data
	code = 0
	msg = "获取成功"

}
