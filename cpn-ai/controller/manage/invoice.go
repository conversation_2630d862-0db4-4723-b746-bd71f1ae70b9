package manage

import (
	"bytes"
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"fmt"
	"net/http"
	"os"
	"path"
	"strings"
	"text/template"
	"time"

	"github.com/shopspring/decimal"

	"github.com/gin-gonic/gin"
)

type invoiceAdminApi struct {
}

type EmailData struct {
	// 发票下载链接
	InvoiceURL string
	// 订单号列表，每个订单号单独一行
	Orders []string
	// 公司名称
	CompanyName string
	Remark      string
}

type invoiceDetailReq struct {
	ID uint `json:"id"`
}

// 管理员查询发票列表请求
type adminInvoiceListReq struct {
	Status   int    `json:"status"`
	KW       string `json:"kw"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

// 发票列表响应项结构体
type invoiceListItemResp struct {
	ID          uint               `json:"id"`
	CreatedAt   jsontime.JsonTime  `json:"created_at"`
	Amount      decimal.Decimal    `json:"amount"`
	CompanyName string             `json:"company_name"`
	Email       string             `json:"email"`
	Address     string             `json:"address"`
	Phone       string             `json:"phone"`
	BankName    string             `json:"bank_name"`
	BankAccount string             `json:"bank_account"`
	TaxId       string             `json:"tax_id"`
	Status      int                `json:"status"`
	StatusText  string             `json:"status_text"`
	IssueTime   *jsontime.JsonTime `json:"issue_time,omitempty"`
	InvoiceNo   string             `json:"invoice_no"`
	InvoiceUrl  string             `json:"invoice_url"`
	InvoiceType int                `json:"invoice_type"`
	Remark      string             `json:"remark"`
}

// 管理员处理发票请求
type adminInvoiceProcessReq struct {
	ID         uint   `json:"id" binding:"required"`
	Agree      bool   `json:"agree"`
	Remark     string `json:"remark"`
	InvoiceUrl string `json:"invoice_url"`
}

// ListInvoices 管理员查询发票列表
func (obj invoiceAdminApi) ListInvoices(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq adminInvoiceListReq
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if claims.UserId <= 0 {
		code = 2
		msg = "请先登录"
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.PageSize < 1 {
		oReq.PageSize = 10
	}
	if oReq.Page < 1 {
		oReq.Page = 1
	}

	var invoice model.Invoice
	ary := make([]invoiceListItemResp, 0)
	if total, err := invoice.ListForAdmin(&ary, oReq.Status, oReq.KW, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询出错"
		logger.Error(msg, err)
		return
	} else {
		// 处理状态文本
		for i := range ary {
			ary[i].StatusText = enums.InvoiceStatusEnum_.Name(ary[i].Status)
		}
		result["items"] = ary
		result["total"] = total
	}

	msg = "发票申请列表"
	code = 0
}

// ProcessInvoice 管理员处理发票
func (obj invoiceAdminApi) ProcessInvoice(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq adminInvoiceProcessReq
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if claims.UserId <= 0 {
		code = 2
		msg = "请先登录"
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var invoice model.Invoice
	if err := invoice.GetById(oReq.ID); err != nil {
		msg = "发票申请不存在"
		logger.Error(msg, err)
		return
	}

	if invoice.Status != enums.InvoiceStatusEnum_.Processing {
		msg = "该发票已处理或者已经拒绝"
		return
	}

	status := enums.InvoiceStatusEnum_.Success
	if !oReq.Agree {
		status = enums.InvoiceStatusEnum_.Failed
	}

	// 更新发票状态为已开票
	if err := invoice.UpdateStatus(status, claims.UserId, oReq.InvoiceUrl, oReq.Remark); err != nil {
		msg = "处理发票失败"
		logger.Error(msg, err)
	}

	// 通知用户
	if invoice.Email != "" {
		emailContent := ""
		var invoiceRechargeRel model.InvoiceRechargeRel
		outTradeNos, err := invoiceRechargeRel.GetOutTradeNosByInvoiceId(invoice.ID)
		if err == nil {
			if oReq.Agree {
				emailContent = common.InvoiceSuccessEmail

			} else {
				emailContent = common.InvoiceFailedEmail
			}
			data := EmailData{
				InvoiceURL: oReq.InvoiceUrl,
				Orders:     outTradeNos,
				Remark:     oReq.Remark,
			}
			// 解析模板
			tmpl, err := template.New("email").Parse(emailContent)
			if err != nil {
				logger.Error("模板解析错误: %v", err)
			}

			// 执行模板并将结果写入 buffer
			var buf bytes.Buffer
			if err = tmpl.Execute(&buf, data); err != nil {
				logger.Error("执行模板错误: %v", err)
			}
			// 发送邮件通知
			logger.Infof("发票申请结果通知,收件人:%s,内容:%s", invoice.Email, buf.String())

			var emailReq = service.EmailReq{
				To:          invoice.Email,
				Subject:     "发票申请结果通知",
				Content:     buf.String(),
				ContentType: common.EmailContentTypeHtml,
			}

			err = service.EmailService.SendFromService(emailReq)
			if err != nil {
				logger.Error("发送邮件错误: %v", err)
			}
		}
	}

	msg = "处理发票成功"
	code = 0
}

func (obj invoiceAdminApi) GetInvoiceDetail(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if claims.UserId <= 0 {
		code = 2
		msg = "请先登录"
		return
	}

	var oReq invoiceDetailReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var invoiceRechargeRel model.InvoiceRechargeRel
	if rechargeOutputNos, err := invoiceRechargeRel.GetOutTradeNosByInvoiceId(oReq.ID); err != nil {
		msg = "发票申请不存在"
		logger.Error(msg, err)
		return
	} else {
		var recharge model.Recharge
		recharges, err := recharge.GetByOutTradeNos(rechargeOutputNos)
		if err != nil {
			msg = "发票对应充值记录查询失败"
			logger.Error(msg, err)
			return
		}
		result["data"] = recharges
		msg = "发票详情"
		code = 0
	}

}

func (obj invoiceAdminApi) UploadFile(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if claims.UserId <= 0 {
		code = 2
		msg = "请先登录"
		return
	}

	invoiceId, exist := c.GetPostForm("invoice_id")
	if !exist {
		msg = "参数解析失败"
		logger.Error(msg, "invoice_id")
		return
	}
	// invoiceId转换成uint
	var invoice model.Invoice
	if err := invoice.GetById(utils.String2Uint(invoiceId)); err != nil {
		msg = "发票申请不存在"
		logger.Error(msg, err)
		return
	}
	if invoice.Status != enums.InvoiceStatusEnum_.Processing {
		msg = "该发票已处理或者已经拒绝"
		return
	}

	f, errf := c.FormFile("file")
	if errf != nil {
		msg = "图片上传失败"
		logger.Error(msg, errf)
		return
	}
	ext := strings.ToLower(path.Ext(f.Filename)) // 输出 .html
	imgTypes := ".jpg.png.jpeg.gif.tiff.avf.pdf"

	if !strings.Contains(imgTypes, ext) {
		msg = "目前支持的图片格式是" + imgTypes
		return
	}

	p := "cpn/invoice/" + invoiceId + "/"
	rootPath := config.DiffusionFilePath + p
	if _, err := os.Stat(rootPath); err != nil {
		if os.IsNotExist(err) {
			if err := os.MkdirAll(rootPath, os.ModePerm); err != nil {
				msg = "创建文件夹出错"
				logger.Error(msg, err, invoiceId)
				return
			}
		} else {
			msg = "路径检测错误"
			logger.Error(msg, err)
			return
		}
	}
	fileName := utils.GetMd5(fmt.Sprintf("%s_%d", f.Filename, time.Now().UnixMicro())) + ext
	filePath := rootPath + fileName
	if _, err := os.Stat(filePath); err != nil {
		if os.IsNotExist(err) {
		} else {
			msg = "路径检测错误"
			logger.Error(msg, err)
			return
		}
	} else {
		msg = "图片已存在，上传失败"
		logger.Error(msg, invoiceId)
		return
	}
	if err := c.SaveUploadedFile(f, filePath); err != nil {
		msg = "图片保存失败"
		logger.Error(msg, err)
		return
	} else {
		msg = "图片上传成功"
		result["file_url"] = config.DiffusionDomain + p + fileName
		code = 0
		return
	}
}

var InvoiceAdminApi invoiceAdminApi
