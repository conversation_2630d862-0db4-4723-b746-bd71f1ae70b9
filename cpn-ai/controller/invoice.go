package controller

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type invoiceApi struct {
}

// 发票申请请求结构体
type applyInvoiceReq struct {
	CompanyName string   `json:"company_name" binding:"required"`
	TaxId       string   `json:"tax_id" binding:"required"`
	Email       string   `json:"email"`
	Address     string   `json:"address"`
	Phone       string   `json:"phone"`
	BankName    string   `json:"bank_name"`
	BankAccount string   `json:"bank_account"`
	InvoiceType int      `json:"invoice_type"`
	OutTradeNos []string `json:"out_trade_nos"`
}

// 发票列表请求结构体
type invoiceListReq struct {
	Status   int `json:"status"`
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}

type invoiceDetailReq struct {
	ID uint `json:"id"`
}

// 发票列表响应项结构体
type invoiceListItemResp struct {
	ID          uint               `json:"id"`
	CreatedAt   jsontime.JsonTime  `json:"created_at"`
	Amount      decimal.Decimal    `json:"amount"`
	CompanyName string             `json:"company_name"`
	Email       string             `json:"email"`
	Address     string             `json:"address"`
	Phone       string             `json:"phone"`
	BankName    string             `json:"bank_name"`
	BankAccount string             `json:"bank_account"`
	TaxId       string             `json:"tax_id"`
	Status      int                `json:"status"`
	StatusText  string             `json:"status_text"`
	IssueTime   *jsontime.JsonTime `json:"issue_time,omitempty"`
	InvoiceNo   string             `json:"invoice_no"`
	InvoiceUrl  string             `json:"invoice_url"`
	InvoiceType int                `json:"invoice_type"`
	Remark      string             `json:"remark"`
}

// ApplyInvoice 申请发票
func (obj invoiceApi) ApplyInvoice(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq applyInvoiceReq
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		code = 2
		msg = "请先登录"
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if len(oReq.OutTradeNos) == 0 {
		msg = "订单号不能为空"
		return
	}

	// 查询订单信息
	var amount = decimal.Zero

	logger.Info("开始申请发票", oReq)

	for _, outTradeNo := range oReq.OutTradeNos {
		var recharge model.Recharge
		var invoiceRechargeRel model.InvoiceRechargeRel
		logger.Info("订单号：", outTradeNo)
		if err := recharge.GetByOutTradeNo(outTradeNo); err != nil {
			msg = "订单不存在"
			logger.Error(msg, err)
			return
		}
		// 验证订单是否属于当前用户
		if recharge.UserId != claims.UserId {
			msg = "无权操作此订单"
			logger.Error(msg, "用户ID不匹配", recharge.UserId, claims.UserId)
			return
		}
		// 验证订单状态是否为已支付
		if recharge.State != enums.RechargeStateEnum.TRADE_SUCCESS {
			msg = "订单未支付，不能申请发票"
			logger.Error(msg, "订单状态", recharge.State)
			return
		}
		// 订单必须是4.27以后的
		if recharge.PayTime.Before(common.InvoiceRechargeStartTime) {
			msg = "历史订单请线下申请"
			logger.Error(msg, "订单创建时间", recharge.CreatedAt)
			return
		}
		// 检查是否已申请过发票
		exists, err := invoiceRechargeRel.CheckInvoiceExists(outTradeNo)
		if err != nil {
			msg = "查询发票状态出错"
			logger.Error(msg, err)
			return
		}
		if exists {
			msg = "该订单已申请过发票"
			return
		}
		amount = amount.Add(recharge.Amount)
	}

	// 创建发票申请记录
	newInvoice := model.Invoice{
		UserId:      claims.UserId,
		Amount:      amount,
		CompanyName: oReq.CompanyName,
		TaxId:       oReq.TaxId,
		Email:       oReq.Email,
		Address:     oReq.Address,
		Phone:       oReq.Phone,
		BankName:    oReq.BankName,
		BankAccount: oReq.BankAccount,
		Status:      enums.InvoiceStatusEnum_.Processing, // 待处理
		InvoiceType: oReq.InvoiceType,
	}

	err := model.DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(&newInvoice).Error; err != nil {
			msg = "申请发票失败"
			logger.Error(msg, err)
			return err
		}
		for _, outTradeNo := range oReq.OutTradeNos {
			invoiceRechargeRel := model.InvoiceRechargeRel{
				InvoiceId:  newInvoice.ID,
				OutTradeNo: outTradeNo,
			}
			if err := tx.Create(&invoiceRechargeRel).Error; err != nil {
				msg = "申请发票失败"
				logger.Error(msg, err)
				return err
			}
		}
		return nil
	})

	if err != nil {
		msg = "申请发票失败"
		logger.Error(msg, err)
		return
	}

	msg = "申请发票成功"
	code = 0
}

// GetInvoiceList 获取用户发票列表
func (obj invoiceApi) GetInvoiceList(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq invoiceListReq
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		code = 2
		msg = "请先登录"
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.PageSize < 1 {
		oReq.PageSize = 10
	}
	if oReq.Page < 1 {
		oReq.Page = 1
	}

	var invoice model.Invoice
	ary := make([]invoiceListItemResp, 0)
	if total, err := invoice.ListByUserId(&ary, claims.UserId, oReq.Status, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询出错"
		logger.Error(msg, err)
		return
	} else {
		for i := range ary {
			ary[i].StatusText = enums.InvoiceStatusEnum_.Name(ary[i].Status)
		}

		result["items"] = ary
		result["total"] = total
	}

	msg = "发票列表"
	code = 0
}

func (obj invoiceApi) GetInvoiceDetail(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		code = 2
		msg = "请先登录"
		return
	}

	var oReq invoiceDetailReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var invoice model.Invoice
	if err := invoice.GetById(oReq.ID); err != nil {
		msg = "发票申请不存在"
		logger.Error(msg, err)
		return
	}

	if invoice.UserId != claims.UserId {
		msg = "无权限"
		logger.Error(msg)
		return
	}

	var invoiceRechargeRel model.InvoiceRechargeRel
	if rechargeOutputNos, err := invoiceRechargeRel.GetOutTradeNosByInvoiceId(oReq.ID); err != nil {
		msg = "发票申请不存在"
		logger.Error(msg, err)
		return
	} else {
		var recharge model.Recharge
		recharges, err := recharge.GetByOutTradeNos(rechargeOutputNos)
		if err != nil {
			msg = "发票对应充值记录查询失败"
			logger.Error(msg, err)
			return
		}
		result["data"] = recharges
		msg = "发票详情"
		code = 0
	}

}

var InvoiceApi invoiceApi
