package controller

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/common/myimg"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	"os"
	"path"
	"runtime"
	"strings"
	"time"
)

type trainApi_ struct {
}

var TrainApi trainApi_

type trainImagesReq struct {
	ImagesUuid string `json:"images_uuid"`
	ImageUrl   string `json:"image_url"`
	Status     int    `json:"status"`
	Kw         string `json:"kw"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

type trainCropTagReq struct {
	ImagesUuid   string  `json:"images_uuid"`
	CropMethod   string  `json:"crop_method"`
	CropSize     string  `json:"crop_size"`
	TagThreshold float64 `json:"tag_threshold"`
	TagAlg       string  `json:"tag_alg"`
	TriggerWord  string  `json:"trigger_word"`
}

type trainImageUploadReq struct {
	CouponCode string `json:"coupon_code"`
	CouponUuid string `json:"coupon_uuid"`
	Status     int    `json:"status"`
	Kw         string `json:"kw"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

type trainImagesListReq struct {
	ImagesUuid string `json:"images_uuid"`
	Status     int    `json:"status"`
	Kw         string `json:"kw"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

type trainImagesResp struct {
}

type trainImagesItemResp struct {
	Uuid         string    `json:"uuid"`
	Title        string    `json:"title"`
	Count        int       `json:"count"`
	ImageUrls    []string  `json:"image_urls"`
	LastUseAt    time.Time `json:"last_use_at"`
	LastUseMilli int64     `json:"last_use_milli"`
	Remark       string    `json:"remark"`
	Status       int       `json:"status"`
}

func (obj trainApi_) ImageUpload(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	imagesUuid, _ := c.GetPostForm("images_uuid")
	if imagesUuid == "" {
		imagesUuid = utils.GetUUID()
	}

	var trainImages model.TrainImages
	if err := trainImages.GetByUuid(imagesUuid); err != nil {
		if err == gorm.ErrRecordNotFound {
			trainImages = model.TrainImages{
				Uuid:      imagesUuid,
				UserId:    claims.UserId,
				LastUseAt: time.Now(),
				Status:    1,
			}
			if err := trainImages.Save(); err != nil {
				msg = "保存数据集失败"
				logger.Error(msg, err)
				return
			}
		} else {
			msg = "查询数据集出错"
			logger.Error(msg, err)
			return
		}
	}
	if trainImages.Uuid == "" {
		msg = "生成数据集失败"
		return
	}
	imagesUuid = trainImages.Uuid
	result["images_uuid"] = imagesUuid

	imagesBasePath := path.Join(config.TrainStorage, "images", imagesUuid)
	viewsBasePath := path.Join(config.TrainStorage, "views", imagesUuid)
	if _, err := os.Stat(imagesBasePath); err != nil {
		if os.IsNotExist(err) {
			if err := os.MkdirAll(imagesBasePath, os.ModePerm); err != nil {
				msg = "创建数据集文件夹出错"
				logger.Error(msg, err)
				return
			}
		}
	}

	if _, err := os.Stat(viewsBasePath); err != nil {
		if os.IsNotExist(err) {
			if err := os.MkdirAll(viewsBasePath, os.ModePerm); err != nil {
				msg = "创建缩略图文件夹出错"
				logger.Error(msg, err)
				return
			}
		}
	}

	f, errf := c.FormFile("file")
	if errf != nil {
		msg = "图片上传失败"
		logger.Error(msg, errf)
		return
	}
	ext := strings.ToLower(path.Ext(f.Filename)) // 输出 .html
	if ext != ".jpg" && ext != ".jpeg" && ext != ".png" && ext != ".webp" {
		msg = "目前图片格式只支持（.jpg .jpeg .png .webp）"
		return
	}
	if f.Size > 10*1024*1024 {
		msg = "单张图片大小不能超过10M"
		return
	}
	imageName := utils.GetUUID()
	imageFileName := imageName + ext
	imageFilePath := path.Join(imagesBasePath, imageFileName)

	viewFilePath := path.Join(viewsBasePath, imageName+ext)

	if err := c.SaveUploadedFile(f, imageFilePath); err != nil {
		msg = "图片保存失败"
		logger.Error(msg, err)
		return
	} else {
		if img, str, err := myimg.FileToImg(imageFilePath); err != nil {
			msg = "读取图片文件失败"
			logger.Error(msg, str, err)
		} else {
			smallImg := myimg.ResizeImg(180, 180, img, true)
			if err := myimg.ImgToJpegFile(smallImg, viewFilePath, 80); err != nil {
				msg = "保存缩略图失败"
				logger.Error(msg, err)
				return
			}
		}
		msg = "文件上传成功"
		code = 0
		return
	}
}

func (obj trainApi_) ImageDel(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainImagesReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.ImageUrl == "" {
		msg = "参数错误"
		return
	}

	if !strings.Contains(oReq.ImageUrl, oReq.ImagesUuid) {
		msg = "参数不匹配"
		return
	}
	imagesUuid := oReq.ImagesUuid
	var trainImages model.TrainImages
	if err := trainImages.GetByUuid(imagesUuid); err != nil {
		msg = "获取数据集信息失败"
		logger.Error(msg, err)
		return
	}
	if trainImages.UserId != claims.UserId {
		msg = "无权限"
		return
	}
	//if trainImages.Status == 1 {
	//	msg = "该数据集正在打标中，不能删除"
	//	return
	//}

	imageFileName := path.Base(oReq.ImageUrl) // 输出 name.html
	//ext := path.Ext(baseName)                      // 输出 .html

	imagesBasePath := path.Join(config.TrainStorage, "images", imagesUuid)
	viewsBasePath := path.Join(config.TrainStorage, "views", imagesUuid)

	imageFilePath := path.Join(imagesBasePath, imageFileName)
	viewFilePath := path.Join(viewsBasePath, imageFileName)

	if err := os.Remove(viewFilePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除缩略图失败"
			logger.Error(msg, err, viewFilePath)
			return
		}
	}

	if err := os.Remove(imageFilePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除图片失败"
			logger.Error(msg, err, imageFilePath)
			return
		}
	}
	msg = "删除成功"
	code = 0
}

func (obj trainApi_) ImagesItem(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainImagesListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.ImagesUuid == "" {
		msg = "数据集参数错误"
		return
	}

	var trainImages model.TrainImages
	if err := trainImages.GetByUuid(oReq.ImagesUuid); err != nil {
		if err == gorm.ErrRecordNotFound {
			msg = "该图片集信息不存在"
			return
		}
		msg = "获取数据集数据失败"
		logger.Error(msg, err)
		return
	}
	imagesUuid := trainImages.Uuid

	var imagesItem trainImagesItemResp
	if err := utils.Scan(trainImages, &imagesItem); err != nil {
		msg = "转换数据失败"
		logger.Error(msg, err)
	}
	imagesItem.LastUseMilli = trainImages.LastUseAt.UnixMilli()

	imagesBasePath := path.Join(config.TrainStorage, "images", imagesUuid)
	if arr, err := readDir(imagesBasePath); err != nil {
		msg = "获取图片列表出错"
		logger.Error(msg, err)
		return
	} else {
		aryUrl := make([]string, 0)
		for _, item := range arr {
			tmpUrl := path.Join("view", imagesItem.Uuid, item.Name)
			aryUrl = append(aryUrl, tmpUrl)
		}
		if len(aryUrl) != imagesItem.Count {
			if err := trainImages.SetCount(len(aryUrl)); err != nil {
				logger.Error(err)
			}
			imagesItem.Count = trainImages.Count
		}
		imagesItem.ImageUrls = aryUrl
		result["images_item"] = imagesItem
	}
	msg = ""
	code = 0
}

func (obj trainApi_) ImagesList(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainImagesListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	var trainImages model.TrainImages
	var ary = make([]trainImagesItemResp, 0)

	queryParm := make(map[string]interface{})

	if oReq.ImagesUuid != "" {
		queryParm["images_uuid"] = oReq.ImagesUuid
	}

	if oReq.Status >= 0 {
		queryParm["status"] = oReq.Status
	}

	if total, err := trainImages.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		empty := make([]string, 0)
		for i := 0; i < len(ary); i++ {
			ary[i].ImageUrls = empty
			ary[i].LastUseMilli = ary[i].LastUseAt.UnixMilli()
		}
		result["images"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj trainApi_) ImagesDel(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainImagesReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	imagesUuid := oReq.ImagesUuid
	var trainImages model.TrainImages
	if err := trainImages.GetByUuid(imagesUuid); err != nil {
		msg = "获取数据集信息失败"
		logger.Error(msg, err)
		return
	}
	if trainImages.UserId != claims.UserId {
		msg = "无权限"
		return
	}
	//if trainImages.Status == 1 {
	//	msg = "该数据集正在打标中，不能删除"
	//	return
	//}

	if len(imagesUuid) < 30 {
		msg = "参数错误"
		return
	}
	imagesBasePath := path.Join(config.TrainStorage, "images", imagesUuid)
	viewsBasePath := path.Join(config.TrainStorage, "views", imagesUuid)

	if err := os.RemoveAll(viewsBasePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除缩略图片集失败"
			logger.Error(msg, err, viewsBasePath)
			return
		}
	}

	if err := os.RemoveAll(imagesBasePath); err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			msg = "删除图片集失败"
			logger.Error(msg, err, imagesBasePath)
			return
		}
	}

	if err := trainImages.Delete(); err != nil {
		msg = "删除图片集数据失败"
		logger.Error(msg, err)
		return
	}

	msg = "删除成功"
	code = 0
}

func (obj trainApi_) ImagesCropTag(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	lockKey := ""
	defer func() {
		if lockKey != "" {
			common.RedisUnLock(lockKey)
		}
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq trainCropTagReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	imagesUuid := oReq.ImagesUuid
	if len(imagesUuid) < 32 {
		msg = "参数错误"
		return
	}

	lockKey = enums.RedisKeyEnum.LockKey + "ImagesCropTag_" + imagesUuid
	if common.RedisLock(lockKey, 1, 1000*10) {

	} else {
		lockKey = ""
		msg = "请勿频繁操作"
		return
	}

	var trainImages model.TrainImages
	if err := trainImages.GetByUuid(imagesUuid); err != nil {
		msg = "获取数据集信息失败"
		logger.Error(msg, err)
		return
	}
	if trainImages.UserId != claims.UserId {
		msg = "无权限"
		return
	}
	if trainImages.Status == model.TrainImagesStatusTagging {
		msg = "已在排队打标中，请勿重复操作"
		return
	}

	trainTags := model.TrainTags{
		Uuid:         utils.GetUUID(),
		UserId:       claims.UserId,
		ImagesUuid:   oReq.ImagesUuid,
		CropMethod:   oReq.CropMethod,
		CropSize:     oReq.CropSize,
		TagThreshold: oReq.TagThreshold,
		TagAlg:       oReq.TagAlg,
		TriggerWord:  oReq.TriggerWord,
		Count:        trainImages.Count,
	}
	if err := trainTags.Save(); err != nil {
		msg = "生成打标数据失败"
		logger.Error(msg, err)
		return
	}

	msg = "加入打标队列成功"
	code = 0
}
