package redisqueue

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"errors"
	"strings"
	"sync"
	"time"
)

const (
	RedisQueueListPrefix = "redisqueue:list:"
	RedisQueueZsetPrefix = "redisqueue:zset:"
	RedisQueueHsetPrefix = "redisqueue:hset:"
)

var LastPopAt sync.Map

type Task struct {
	QTaskId          string  `json:"q_task_id"`
	QTaskPushAt      int64   `json:"q_task_push_at"`
	QTaskUpdatedAt   int64   `json:"q_task_updated_at"`
	QTaskStartAt     int64   `json:"q_task_start_at"`
	QTaskCompletedAt int64   `json:"q_task_completed_at"`
	QTaskProgress    float64 `json:"q_task_progress"`
	QTaskInput       any     `json:"q_task_input"`
	QTaskOutput      any     `json:"q_task_output"`
}

func Push(queueName string, taskJson string) (string, error) {
	m := utils.GetMapFromJson(taskJson)
	qTaskId := ""
	if v, ok := m["q_task_id"]; ok {
		qTaskId = v.(string)
	}
	if qTaskId == "" {
		return qTaskId, errors.New("q_task_id field is not exist")
	}
	m["q_task_push_at"] = time.Now().UnixMilli()
	m["q_task_updated_at"] = time.Now().UnixMilli()
	m["q_task_start_at"] = 0
	m["q_task_completed_at"] = 0
	m["q_task_progress"] = -1 // -1排队中  >=0<100执行中 >=100执行完成

	taskJson = utils.GetJsonFromStruct(m)

	listKey := RedisQueueListPrefix + queueName
	_, err := common.RedisLPush(listKey, qTaskId)
	if err != nil {
		return qTaskId, err
	} else {
		hsetKey := RedisQueueHsetPrefix + queueName
		if _, err := common.RedisHSet(hsetKey, qTaskId, taskJson); err != nil {
			return qTaskId, err
		}

		zsetKey := RedisQueueZsetPrefix + queueName
		if _, err := common.RedisZAdd(zsetKey, qTaskId, float64(time.Now().UnixMilli())); err != nil {
			return qTaskId, err
		}
		return qTaskId, nil
	}
}

func PushTask(queueName string, qTaskInput any) (string, error) {
	qTaskId := utils.GetUUID()
	task := Task{
		QTaskId:        qTaskId,
		QTaskPushAt:    time.Now().UnixMilli(),
		QTaskUpdatedAt: time.Now().UnixMilli(),
		QTaskProgress:  -1,
		QTaskInput:     qTaskInput,
	}

	taskJson := utils.GetJsonFromStruct(task)
	listKey := RedisQueueListPrefix + queueName
	_, err := common.RedisLPush(listKey, taskJson)
	if err != nil {
		return qTaskId, err
	} else {
		zsetKey := RedisQueueZsetPrefix + queueName
		if _, err := common.RedisZAdd(zsetKey, qTaskId, float64(time.Now().UnixMilli())); err != nil {
			return qTaskId, err
		}

		//hsetKey := RedisQueueHsetPrefix + queueName
		//if _, err := common.RedisHSet(hsetKey, qTaskId, taskJson); err != nil {
		//	return qTaskId, err
		//}
		return qTaskId, nil
	}
}

/*
func Pop(queueName string) (string, error) {
	listKey := RedisQueueListPrefix + queueName
	if taskJson, err := common.RedisBRPop(listKey); err != nil {
		return "", err
	} else {
		m := utils.GetMapFromJson(taskJson)
		qTaskId := ""
		if v, ok := m["q_task_id"]; ok {
			qTaskId = v.(string)
		}
		if qTaskId == "" {
			return "", errors.New("q_task_id is empty string")
		}

		hsetKey := RedisQueueHsetPrefix + queueName
		if taskJson, err := common.RedisHGet(hsetKey, qTaskId); err != nil {
			return taskJson, err
		} else {
			if taskJson == "" {
				return "", errors.New("task is empty")
			}
			if err := Progress(queueName, qTaskId, 0); err != nil {

			} else {
				RemoveRank(queueName, qTaskId)
			}
			return taskJson, nil
		}
	}
}*/

func PopTask(queueName string) (Task, error) {
	var task Task
	listKey := RedisQueueListPrefix + queueName
	if taskJson, err := common.RedisBRPop(listKey); err != nil {
		return task, err
	} else {
		if taskJson == "" {
			return task, errors.New("taskJson is empty string")
		}
		if err := utils.GetStructFromJson(&task, taskJson); err != nil {
			return task, err
		} else {
			LastPopAt.Store(queueName, task.QTaskPushAt)
		}
		hsetKey := RedisQueueHsetPrefix + queueName
		task.QTaskProgress = 0
		if _, err := common.RedisHSet(hsetKey, task.QTaskId, taskJson); err != nil {
			return task, err
		}
		RemoveRank(queueName, task.QTaskId)
		return task, nil
	}
}

func Progress(queueName string, qTaskId string, progress float64) error { //设置任务进度 progress为0时设置自动设置开始时间
	hsetKey := RedisQueueHsetPrefix + queueName
	if taskJson, err := common.RedisHGet(hsetKey, qTaskId); err != nil {
		return err
	} else {
		if taskJson == "" {
			return errors.New("task is empty")
		}
		var task Task
		if err := utils.GetStructFromJson(&task, taskJson); err != nil {
			return err
		} else {
			task.QTaskProgress = progress
			task.QTaskUpdatedAt = time.Now().UnixMilli()
			if progress == 0 {
				task.QTaskStartAt = time.Now().UnixMilli()
			}
			taskJson = utils.GetJsonFromStruct(task)
			if _, err := common.RedisHSet(hsetKey, qTaskId, taskJson); err != nil {
				return err
			} else {
				return nil
			}
		}
	}
}

func Completed(queueName string, qTaskId string, qTaskOutput any) error { //设置任务进度 progress为0时设置自动设置开始时间 progress为100时自动设置完成时间
	hsetKey := RedisQueueHsetPrefix + queueName
	if taskJson, err := common.RedisHGet(hsetKey, qTaskId); err != nil {
		return err
	} else {
		if taskJson == "" {
			return errors.New("task is empty")
		}
		var task Task
		if err := utils.GetStructFromJson(&task, taskJson); err != nil {
			return err
		} else {
			task.QTaskOutput = qTaskOutput
			task.QTaskCompletedAt = time.Now().UnixMilli()
			task.QTaskUpdatedAt = time.Now().UnixMilli()
			taskJson = utils.GetJsonFromStruct(task)
			if _, err := common.RedisHSet(hsetKey, qTaskId, taskJson); err != nil {
				return err
			} else {
				return nil
			}
		}
	}
}

func RemoveRank(queueName string, qTaskId string) (int64, error) { //从排序队列中移除 qTaskId不存在 返回0,nil qTaskId存在并且移除成功返回1,nil
	zsetKey := RedisQueueZsetPrefix + queueName
	if exists, err := common.RedisZRem(zsetKey, qTaskId); err != nil {
		return exists, err
	} else {
		return exists, err
	}
}

func RemoveHash(queueName string, qTaskId string) (int64, error) { //从哈希中移除 qTaskId不存在 返回0,nil qTaskId存在并且移除成功返回1,nil
	zsetKey := RedisQueueZsetPrefix + queueName
	if index, err := common.RedisZRem(zsetKey, qTaskId); err != nil {
		return index, err
	} else {
		hsetKey := RedisQueueHsetPrefix + queueName
		if exists, err := common.RedisHDel(hsetKey, qTaskId); err != nil {
			return exists, err
		} else {
			return exists, err
		}
	}
}

func Rank(queueName string, qTaskId string) (int64, error) {
	zsetKey := RedisQueueZsetPrefix + queueName
	if index, err := common.RedisZRank(zsetKey, qTaskId); err != nil {
		return index, err
	} else {
		return index, err
	}
}

func Clear() error { //清除残余Redis数据
	hsetKeys, err := common.RedisScanKeys(RedisQueueHsetPrefix + "*")
	if err != nil {
		logger.Error(err)
		return err
	}
	count := int64(100)
	for _, hsetKey := range hsetKeys {
		cursor := uint64(0)
		queueName := strings.TrimLeft(hsetKey, RedisQueueHsetPrefix)
		CheckAndClearRank(queueName)
		for {
			if nextCursor, mTask, err := common.RedisHScan(hsetKey, cursor, count); err != nil {
				logger.Error(err)
				break
			} else {
				cursor = nextCursor
				CheckAndClearHash(queueName, mTask)
				if nextCursor == 0 || len(mTask) == 0 {
					break
				}
			}
		}
	}
	return nil
}

func CheckAndClearRank(queueName string) error {
	zsetKey := RedisQueueZsetPrefix + queueName
	slice := common.RedisZRangeWithScores(zsetKey, 50)
	if len(slice.Val()) == 0 {
		return nil
	}
	tenSeconds := int64(10 * 1000)
	for _, z := range slice.Val() {
		qTaskId := z.Member.(string)
		qTaskPushAt := int64(z.Score)

		if pushAt, ok := LastPopAt.Load(queueName); !ok {

		} else {
			lastPushAt := pushAt.(int64)
			if qTaskPushAt < lastPushAt-tenSeconds {
				RemoveRank(queueName, qTaskId)
			}
			if qTaskPushAt > lastPushAt {
				break
			}
		}
	}
	return nil
}

func CheckAndClearHash(queueName string, mTask map[string]string) {
	clearTaskIds := make([]string, 0)
	for taskId, taskValue := range mTask {
		if taskValue == "" {
			clearTaskIds = append(clearTaskIds, taskId)
			continue
		}
		var task Task
		if err := utils.GetStructFromJson(&task, taskValue); err != nil {
			logger.Error("CheckAndClear GetStructFromJson err:", err)
		} else {
			completedOutMilli := int64(24 * 1 * 3600000)
			fiveMinutes := int64(5 * 60 * 1000)
			tenSeconds := int64(10 * 1000)
			if task.QTaskCompletedAt <= 0 {
				if pushAt, ok := LastPopAt.Load(queueName); !ok {

				} else {
					lastPushAt := pushAt.(int64)
					if task.QTaskPushAt < lastPushAt-tenSeconds {
						if task.QTaskUpdatedAt < time.Now().UnixMilli()-fiveMinutes { //5分钟没有进度更新
							if task.QTaskUpdatedAt < time.Now().UnixMilli()-completedOutMilli {
								clearTaskIds = append(clearTaskIds, taskId)
								continue
							}
						}
					}
				}

			} else {
				if task.QTaskCompletedAt < time.Now().UnixMilli()-completedOutMilli {
					clearTaskIds = append(clearTaskIds, taskId)
					continue
				}
			}
		}
	}
	for _, taskId := range clearTaskIds {
		RemoveHash(queueName, taskId)
	}
}
