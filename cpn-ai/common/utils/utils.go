package utils

import (
	"bytes"
	"context"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"html/template"
	"io/ioutil"
	"log"
	"math/rand"
	"net"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"reflect"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"
	"unsafe"
)

func OpenBrowser(url string) {
	var err error

	switch runtime.GOOS {
	case "linux":
		err = exec.Command("xdg-open", url).Start()
	case "windows":
		err = exec.Command("rundll32", "url.dll,FileProtocolHandler", url).Start()
	case "darwin":
		err = exec.Command("open", url).Start()
	}
	if err != nil {
		log.Println(err)
	}
}

func GetIp() (ip string) {
	ips, err := net.InterfaceAddrs()
	if err != nil {
		log.Println(err)
		return ip
	}

	for _, a := range ips {
		if ipNet, ok := a.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ipNet.IP.To4() != nil {
				ip = ipNet.IP.String()
				if strings.HasPrefix(ip, "10") {
					return
				}
				if strings.HasPrefix(ip, "172") {
					return
				}
				if strings.HasPrefix(ip, "192.168") {
					return
				}
				ip = ""
			}
		}
	}
	return
}

func GetClientIp(header http.Header) string {
	xForwardedFor := header.Get("X-Forwarded-For")
	ary := strings.Split(xForwardedFor, ",")
	for _, value := range ary {
		if !strings.HasPrefix(value, "192") {
			return value
		}
	}
	return ""
}

func GetLocalIP() string {
	// 获取本地所有网络接口
	interfaces, err := net.Interfaces()
	if err != nil {
		logger.Error(err)
	}

	// 遍历网络接口
	for _, iface := range interfaces {
		// 忽略loopback接口（如lo0）和没有物理地址的接口
		if iface.Flags&net.FlagUp == 0 || iface.Flags&net.FlagLoopback != 0 {
			continue
		}

		// 获取每个接口的地址
		addrs, err := iface.Addrs()
		if err != nil {
			logger.Error(err)
		}

		// 遍历接口的所有地址
		for _, addr := range addrs {
			// 确保地址是IPv4格式
			ipnet, ok := addr.(*net.IPNet)
			if ok && ipnet.IP.To4() != nil {
				return ipnet.IP.String()
			}
		}
	}
	return ""
}

// 提取性别和出生日期
func ExtractIdCard(idCard string) (string, time.Time) {
	if len(idCard) != 18 {
		return "", time.Time{}
	}

	// 提取出生日期（身份证号的第7到第14位）
	birthDateStr := idCard[6:14]
	birthDate, err := time.Parse("20060102", birthDateStr)
	if err != nil {
		return "", time.Time{}
	}

	// 提取性别（身份证号的第17位，奇数为男性，偶数为女性）
	genderDigit := idCard[16:17]
	genderInt, err := strconv.Atoi(string(genderDigit))
	if err != nil {
		return "", time.Time{}
	}

	// 根据奇偶判断性别
	var gender string
	if genderInt%2 == 0 {
		gender = "女性"
	} else {
		gender = "男性"
	}

	return gender, birthDate
}

var sizeKB = 1024
var sizeMB = sizeKB * 1024
var sizeGB = sizeMB * 1024

func Bytes2Size(num int64) string {
	numStr := ""
	unit := "B"
	if num/int64(sizeGB) > 1 {
		numStr = fmt.Sprintf("%.2f", float64(num)/float64(sizeGB))
		unit = "GB"
	} else if num/int64(sizeMB) > 1 {
		numStr = fmt.Sprintf("%d", int(float64(num)/float64(sizeMB)))
		unit = "MB"
	} else if num/int64(sizeKB) > 1 {
		numStr = fmt.Sprintf("%d", int(float64(num)/float64(sizeKB)))
		unit = "KB"
	} else {
		numStr = fmt.Sprintf("%d", num)
	}
	return numStr + " " + unit
}

func Seconds2Time(num int64) (time string) {
	if num/31104000 > 0 {
		//time += strconv.Itoa(num/31104000) + " 年 "
		time += fmt.Sprintf("%d年", num/31104000)
		num %= 31104000
	}
	if num/2592000 > 0 {
		//time += strconv.Itoa(num/2592000) + " 个月 "
		time += fmt.Sprintf("%d个月", num/2592000)
		num %= 2592000
	}
	if num/86400 > 0 {
		//time += strconv.Itoa(num/86400) + " 天 "
		time += fmt.Sprintf("%d天", num/86400)
		num %= 86400
	}
	if num/3600 > 0 {
		//time += strconv.Itoa(num/3600) + " 小时 "
		time += fmt.Sprintf("%d小时", num/3600)
		num %= 3600
	}
	if num/60 > 0 {
		//time += strconv.Itoa(num/60) + " 分钟 "
		time += fmt.Sprintf("%d分钟", num/60)
		num %= 60
	}
	//time += strconv.Itoa(num) + " 秒"
	time += fmt.Sprintf("%d秒", num)
	return
}

func Interface2String(inter interface{}) string {
	switch inter.(type) {
	case string:
		return inter.(string)
	case int:
		return fmt.Sprintf("%d", inter.(int))
	case float64:
		return fmt.Sprintf("%f", inter.(float64))
	}
	return "Not Implemented"
}

func MonthNum2Time(monthNum int) (time.Time, error) {
	monthNumStr := Int2String(monthNum)
	// 使用 time.Parse 解析日期字符串
	if parsedTime, err := time.ParseInLocation("200601", monthNumStr, time.Now().Location()); err != nil {
		logger.Error(err)
		return time.Time{}, err
	} else {
		return parsedTime, nil
	}
}

func UnescapeHTML(x string) interface{} {
	return template.HTML(x)
}

func IntMax(a int, b int) int {
	if a >= b {
		return a
	} else {
		return b
	}
}

func GetMd5(oMd5Str string) string {

	has := md5.Sum([]byte(oMd5Str))
	md5Str := hex.EncodeToString(has[:])
	if len(md5Str) != 32 {
		logger.Error("err md5位数不正确，", md5Str, "  ", oMd5Str)
		return ""
	}
	return md5Str
}

func GetUUID() string {
	code := uuid.New().String()
	code = strings.Replace(code, "-", "", -1)
	return code
}

//func NewNanoId() string {
//	id, _ := gonanoid.New()
//	return id
//}

func Generate12NanoId() string {
	// 定义一个只包含小写字母和数字的字符集
	const customAlphabet = "0123456789abcdefghijklmnopqrstuvwxyz"
	id, _ := gonanoid.Generate(customAlphabet, 12)
	return id
}

// 使用 SHA-256 和随机盐对密码进行加密
func EncryptPassword(password string) (string, string, error) {
	// 生成随机盐
	salt := make([]byte, 16)
	_, err := rand.Read(salt)
	if err != nil {
		return "", "", err
	}

	// 将密码和盐拼接起来
	hash := sha256.New()
	hash.Write([]byte(password))
	hash.Write(salt)
	hashedPassword := hash.Sum(nil)

	// 返回加密后的密码和盐值
	return hex.EncodeToString(hashedPassword), hex.EncodeToString(salt), nil
}

// PathExists 判断一个文件或文件夹是否存在
// 输入文件路径，根据返回的bool值来判断文件或文件夹是否存在
func PathFileExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

// 出错或不存在返回false
func FileExists(path string) bool {
	_, err := os.Stat(path)
	if err == nil {
		return true
	}
	if os.IsNotExist(err) {
		return false
	}
	return false
}

// 计算文件夹大小
func CalculateSize(path string) int64 {
	var size int64

	// 获取文件或文件夹信息
	fileInfo1, err := os.Stat(path)
	if err != nil {
		logger.Error("path:", path, " 无法获取文件或文件夹信息 err:", err)
		return -1
	}

	// 如果是文件，直接返回文件大小
	if !fileInfo1.IsDir() {
		return fileInfo1.Size()
	}

	// 如果是文件夹，遍历文件夹中的所有文件和子文件夹，并累加大小
	filepath.Walk(path, func(filePath string, info os.FileInfo, err error) error {
		if err != nil {
			logger.Error("path:", path, " filePath:", filePath, " 无法访问文件或文件夹 err:", err)
			return nil
		}
		// 跳过目录本身
		if filePath != path {
			size += info.Size()
		}
		return nil
	})

	return size
}

func CalculateSizeWithTimeout(path string, timeout time.Duration) int64 { //-1 超时 -2不存在 -3出错
	var size int64

	// 创建一个带超时的context
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 获取文件或文件夹信息
	fileInfo1, err := os.Stat(path)
	if err != nil {
		if os.IsNotExist(err) {
			//logger.Error("path:", path, " 文件或文件夹不存在 err:", err)
			return -2
		}
		logger.Error("path:", path, " 无法获取文件或文件夹信息 err:", err)
		return -3
	}

	// 如果是文件，直接返回文件大小
	if !fileInfo1.IsDir() {
		return fileInfo1.Size()
	}

	isTimeout := false

	// 如果是文件夹，遍历文件夹中的所有文件和子文件夹，并累加大小
	var hasErr error
	filepath.Walk(path, func(filePath string, info os.FileInfo, err error) error {
		if err != nil {
			logger.Error("path:", path, " filePath:", filePath, " 无法访问文件或文件夹 err:", err)
			hasErr = err
			return err
		}
		// 跳过目录本身
		if filePath != path {
			size += info.Size()
		}

		// 检查context是否已取消（即是否超时）
		if ctx.Err() != nil {
			isTimeout = true
			return ctx.Err()
		}
		return nil
	})
	if isTimeout {
		logger.Error("CalculateSizeWithTimeout 超时")
		return -1
	}
	if hasErr != nil {
		return -3
	}
	return size

}

const keyChars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"

func init() {
	rand.Seed(time.Now().UnixNano())
}

func GenerateKey() string {
	rand.Seed(time.Now().UnixNano())
	key := make([]byte, 48)
	for i := 0; i < 16; i++ {
		key[i] = keyChars[rand.Intn(len(keyChars))]
	}
	uuid_ := GetUUID()
	for i := 0; i < 32; i++ {
		c := uuid_[i]
		if i%2 == 0 && c >= 'a' && c <= 'z' {
			c = c - 'a' + 'A'
		}
		key[i+16] = c
	}
	return string(key)
}

func GeneralSk() (string, error) {
	const apiKeyLength = 42
	prefix := "sk-"

	const alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	// 初始化一个字节切片用于存放生成的密钥
	randomChars := make([]byte, apiKeyLength)
	_, err := rand.Read(randomChars)
	if err != nil {
		return "", fmt.Errorf("failed to read random data: %w", err)
	}
	// 确保密钥包含混合字符，这里简单示例，实际可根据需要调整
	for i := range randomChars {
		// 选择一个随机索引到字符集合中
		characterIndex := rand.Intn(len(alphabet))
		randomChars[i] = alphabet[characterIndex]
	}

	apiKey := prefix + string(randomChars)

	return apiKey, nil
}

func CreateCaptcha(num int) string {
	str := "1"
	for i := 0; i < num; i++ {
		str += strconv.Itoa(0)
	}
	str10 := str
	int10, err := strconv.ParseInt(str10, 10, 32)
	if err != nil {
		fmt.Println(err)
		return ""
	} else {
		j := int32(int10)
		return fmt.Sprintf("%0"+strconv.Itoa(num)+"v", rand.New(rand.NewSource(time.Now().UnixNano())).Int31n(j))
	}
}

func GetRandomString(length int) string {
	rand.Seed(time.Now().UnixNano())
	key := make([]byte, length)
	for i := 0; i < length; i++ {
		key[i] = keyChars[rand.Intn(len(keyChars))]
	}
	return string(key)
}

func GetTimestamp() int64 {
	return time.Now().Unix()
}

func GetTimeString() string {
	now := time.Now()
	return fmt.Sprintf("%s%d", now.Format("20060102150405"), now.UnixNano()%1e9)
}

func MakeInterfaceMap() map[string]interface{} {
	return make(map[string]interface{})
}

func Max(a int, b int) int {
	if a >= b {
		return a
	} else {
		return b
	}
}

func GetOrDefault(env string, defaultValue int) int {
	if env == "" || os.Getenv(env) == "" {
		return defaultValue
	}
	num, err := strconv.Atoi(os.Getenv(env))
	if err != nil {
		logger.Error(fmt.Sprintf("failed to parse %s: %s, using default value: %d", env, err.Error(), defaultValue))
		return defaultValue
	}
	return num
}

func MessageWithRequestId(message string, id string) string {
	return fmt.Sprintf("%s (request id: %s)", message, id)
}

// 生成大写字母和数字的组合的 6 位字符串，不包括字母 'O' 和数字 '0'
func GenerateCouponString(num int) string {
	// 定义字符集合
	charSet := "ABCDEFGHIJKLMNPQRSTUVWXYZ123456789"

	// 设置随机种子
	source := rand.NewSource(time.Now().UnixNano())
	randGen := rand.New(source)

	// 生成 6 位字符串
	result := make([]byte, num)
	for i := range result {
		result[i] = charSet[randGen.Intn(len(charSet))]
	}
	return string(result)
}

// 生成指定位数的随机数
func GenerateRandomNumber(digits int) int {
	// 创建随机种子
	source := rand.NewSource(time.Now().UnixNano())
	randGen := rand.New(source)

	// 计算随机数的上下限
	min := 1
	max := 1
	for i := 0; i < digits-1; i++ {
		min *= 10
		max *= 10
	}
	max = max*10 - 1

	// 生成指定位数的随机数
	return randGen.Intn(max-min+1) + min
}

func GenerateRandomNumberStr(digits int) string {
	// 定义字符集合
	charSet := "0123456789"

	// 设置随机种子
	source := rand.NewSource(time.Now().UnixNano())
	randGen := rand.New(source)

	// 生成 6 位字符串
	result := make([]byte, digits)
	for i := range result {
		result[i] = charSet[randGen.Intn(len(charSet))]
	}
	return string(result)
}

func ValidCouponString(str string, num int) bool {
	regExp := fmt.Sprintf("^[ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789]{%d}$", num)

	// 编译正则表达式
	reg := regexp.MustCompile(regExp)

	// 匹配字符串是否符合规则
	return reg.MatchString(str)
}

func IsMobile(mobile string) bool {
	result, _ := regexp.MatchString(`^(1[3|4|5|6|7|8|9][0-9]\d{4,8})$`, mobile)
	if result {
		return true
	} else {
		return false
	}
}

// 判断字符串是否为有效的邮箱
func IsEmail(email string) bool {
	// 定义邮箱正则表达式
	// 该正则表达式简单匹配邮箱规则，适用于常见的邮箱格式
	const emailPattern = `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`

	// 编译正则表达式
	re := regexp.MustCompile(emailPattern)

	// 使用正则表达式匹配邮箱
	return re.MatchString(email)
}

func IsEduEmail(email string) bool {
	if !IsEmail(email) {
		return false
	}
	ary := strings.Split(email, ".")
	if len(ary) < 3 {
		return false
	}
	if ary[len(ary)-2] == "edu" {
		return true
	}
	return false
}

func IsVersion(code string) bool {
	// 定义正则表达式，匹配只包含数字、字母、点号和连字符的字符串，且不能以点号、连字符开头或结尾
	validRegex := regexp.MustCompile(`^[a-zA-Z0-9](?:[a-zA-Z0-9.\-]*[a-zA-Z0-9])?$`)

	// 使用正则表达式验证字符串
	return validRegex.MatchString(code)
}

func GenInvitationCode() string {
	rand.NewSource(time.Now().UnixNano())
	const charset = "ABCDEFGHIJKLMNPQRSTUVWXYZ123456789"
	b := make([]byte, 6)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	invitationCode := string(b)
	return invitationCode
}

func FormatMobileStar(mobile string) string {
	if len(mobile) <= 10 {
		return mobile
	}
	return mobile[:3] + "****" + mobile[7:]
}

func FormatAccountStar(account string) string {
	length := utf8.RuneCountInString(account)
	if length == 2 {
		return string(account[0]) + "*"
	} else {
		third := length / 3
		startRunes := []rune(account)[:third]
		endRunes := []rune(account)[length-third:]

		return string(startRunes) + strings.Repeat("*", length-2*third) + string(endRunes)
	}
}

func FormatNameStar(name string) string {
	if len(name) <= 1 {
		return name
	}
	result := []rune(name)
	for i := range result {
		if i != 1 {
			result[i] = '*'
		}
	}
	return string(result)
}

func FormatNoStar(no string) string {
	if len(no) <= 4 {
		return no
	}
	result := []rune(no)
	for i := range result {
		if i < len(no)-4 {
			result[i] = '*'
		}
	}
	return string(result)
}

func Substring(s string, start, length int) string {

	rs := []rune(s)

	l := len(rs)

	end := start + length

	if start < 0 || start >= l || end < start || end > l {

		return ""

	}

	return string(rs[start:end])

}

func ReplaceSearchKeyword(input string) string {
	// 定义一个正则表达式，匹配特殊字符
	re := regexp.MustCompile(`[{}|,<>?&;%'"]+`)
	// 使用空字符串替换所有匹配的特殊字符
	cleaned := re.ReplaceAllString(input, "")

	// 返回清理后的字符串
	return cleaned
}

func String2Int(str string) int {
	num, err := strconv.Atoi(str)
	if err != nil {
		return 0
	}
	return num
}

func String2Uint(s string) uint {
	if s == "" {
		return 0
	}
	num, err := strconv.ParseUint(s, 10, 32)
	if err != nil {
		logger.Error(err, "s:", s)
		return 0
	}
	return uint(num)
}

func String2Uint64(s string) uint64 {
	num, err := strconv.ParseUint(s, 10, 32)
	if err != nil {
		logger.Error(err)
		return 0
	}
	return num
}

func String2Int64(s string) int64 {
	num, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		_, file, line, _ := runtime.Caller(1)
		logger.Error(fmt.Sprintf("Error in %s:%d - %v", file, line, err))
		return 0
	}
	return num
}

func Int2String(num int) string {
	return strconv.Itoa(num)
}
func Int642String(num int64) string {
	return fmt.Sprintf("%d", num)
}
func Uint2String(num uint) string {
	return strconv.FormatUint(uint64(num), 10)
}

func Ids2UintAry(ids string) []uint {
	ary := make([]uint, 0)
	splitAry := strings.Split(ids, "|")
	for _, tmp := range splitAry {
		if i := String2Uint(tmp); i > 0 {
			ary = append(ary, i)
		}
	}
	return ary
}

func GetStructFromJson(dest interface{}, jsonStr string) error { //传进来的dest 要加&
	if err := json.Unmarshal([]byte(jsonStr), dest); err != nil {
		logger.Error(err, "===", jsonStr, "===")
		return err
	}
	return nil
}

func GetStructFromMap(dest interface{}, mm map[string]interface{}) error { //传进来的dest 要加&
	jsonStr := GetJsonFromStruct(mm)
	if err := json.Unmarshal([]byte(jsonStr), dest); err != nil {
		logger.Error(err, "===", jsonStr, "===")
		return err
	}
	return nil
}

func GetJsonFromStruct(m interface{}) string {
	jsonbyte, err := json.Marshal(m)
	if err != nil {
		logger.Error(err)
		return ""
	}
	return string(jsonbyte)
}

func GetBytesFromStruct(m interface{}) []byte {
	jsonbyte, err := json.Marshal(m)
	if err != nil {
		logger.Error(err)
		return jsonbyte
	}
	return jsonbyte
}

func GetMapFromJson(jsonStr string) map[string]interface{} {
	var data map[string]interface{}
	// 反序列化 JSON 字符串
	err := json.Unmarshal([]byte(jsonStr), &data)
	if err != nil {
		logger.Error(err, jsonStr)
		return nil
	}
	return data
}

func GetMapObjFromJson(jsonStr string) map[string]interface{} {
	data := make(map[string]interface{})
	return data
}

func GetMapAryFromJson(jsonStr string) []map[string]interface{} {
	var data []map[string]interface{}
	// 反序列化 JSON 字符串
	err := json.Unmarshal([]byte(jsonStr), &data)
	if err != nil {
		// 获取调用者信息
		pc, file, line, _ := runtime.Caller(1)
		funcName := runtime.FuncForPC(pc).Name()
		logger.Error(err, " jsonStr：", jsonStr, "  pc:", pc, " file:", file, " line:", line, " funcName:", funcName)
		return nil
	}
	return data
}

func GetStructAryFromJson(ary interface{}, jsonStr string) error { //ary是地址 需要加&  在外面第一结构体数组 var data []struct
	//var data []map[string]interface{}
	// 反序列化 JSON 字符串
	err := json.Unmarshal([]byte(jsonStr), ary)
	if err != nil {
		logger.Error(err, "jsonStr：", jsonStr)
		return err
	}
	return nil
}

func Scan(source interface{}, target interface{}) error {

	mSource := make(map[string]reflect.Value)
	if err := StructScan2Map(source, mSource); err != nil {
		logger.Error(err)
		return err
	}
	targetValue := reflect.ValueOf(target).Elem()
	targetType := reflect.TypeOf(target).Elem()
	for i := 0; i < targetType.NumField(); i++ {
		field := targetValue.Field(i)
		fieldName := targetType.Field(i).Name
		//typeName := targetType.Field(i).Type.Name()

		if !field.CanSet() {
			continue
		}
		if val, ok := mSource[fieldName]; ok {
			//fmt.Println(val.Type(), "        ", field.Type())
			if val.Type() == field.Type() {
				field.Set(val)
			} else if val.Type() == reflect.TypeOf(time.Time{}) {
				if field.Type() == reflect.TypeOf(jsontime.JsonTime{}) {
					t := jsontime.JsonTime(val.Interface().(time.Time))
					field.Set(reflect.ValueOf(t))
				}
			}
		}
	}
	//fmt.Println(target)
	return nil
}

func StructScan2Map(source interface{}, m map[string]reflect.Value) error {
	data_t := reflect.TypeOf(source) //获得结构体的类型
	//fmt.Println("数据结构体的类型为:", data_t.Name())
	data_v := reflect.ValueOf(source)        //获得结构体所有字段值
	for i := 0; i < data_t.NumField(); i++ { //遍历结构体类型的所有字段
		data_f := data_t.Field(i)
		val := data_v.Field(i)
		//fmt.Printf("字段名:%s, 字段类型:%v,  字段值:%v\n", data_f.Name, data_f.Type, val.Interface())

		typeName := data_f.Type.Name()
		typeNames := "Time,DeletedAt,JsonTime"
		if strings.Contains(typeNames, typeName) {
			m[data_f.Name] = val
		} else if val.Kind() == reflect.Struct {
			StructScan2Map(val.Interface(), m)
		} else {
			m[data_f.Name] = val
		}
	}
	return nil
}

func CheckUrl(url string) bool {
	// 设置超时时间为 3 秒
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	// 发送 GET 请求
	resp, err := client.Get(url)
	if err != nil {
		// 请求失败
		fmt.Println("Error:", err)
		return false
	}
	defer resp.Body.Close()

	// 检查 HTTP 响应状态码
	if resp.StatusCode == http.StatusOK {
		// 200 OK，表示链接可访问
		return true
	}

	// 其他状态码，链接不可访问
	return false
}

func Post(url string, payload string) (string, error) {
	logger.Info(url, "   post payload:", payload)
	//authorization := "Basic eXhqOnl4ajEyMw=="
	req, err := http.NewRequest("POST", url, bytes.NewBuffer([]byte(payload)))
	if err != nil {
		logger.Error("NewRequest error:", err)
		return "", err
	}
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	//req.Header.Set("Authorization", authorization)

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		logger.Error("Post Fatal error:", err)
		return "", err
	}

	defer res.Body.Close()

	content, err := ioutil.ReadAll(res.Body)
	if err != nil {
		logger.Error("Fatal error ", err)
		return "", err
	}

	str := (*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	return *str, nil
}

func Get(url string, headers map[string]string, cookies map[string]string) (*http.Response, error) {
	//authorization := "Basic eXhqOnl4ajEyMw=="
	//payload := ""
	//req, err := http.NewRequest("GET", url, bytes.NewBuffer([]byte(payload)))
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logger.Error("NewRequest error:", err)
		return nil, err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}
	if cookies != nil {
		for key, value := range cookies {
			cookie := &http.Cookie{
				Name:  key,
				Value: value,
			}
			req.AddCookie(cookie)
		}
	}

	//req.Header.Set("Content-Type", "application/json;charset=utf-8")
	//req.Header.Set("Authorization", authorization)

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		logger.Error("Post Fatal error:", err)
	}

	//defer res.Body.Close()

	return res, err
	//data := res.Cookies()
	//var ttwid string
	//for _, c := range data {
	//	if c.Name == "ttwid" {
	//		ttwid = c.Value
	//		break
	//	}
	//}
	//fmt.Println(ttwid)
	//
	//content, err := ioutil.ReadAll(res.Body)
	//if err != nil {
	//	logger.Error("Fatal error ", err)
	//	return "", err
	//}
	//
	//str := (*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	//return *str, nil
}
func UrlJoin(a string, b string) string {
	if !strings.HasSuffix(a, "/") {
		a = a + "/"
	}
	if strings.HasPrefix(b, "/") {
		b = b[1:len(b)]
	}
	return a + b
}

// Contains 函数用于判断字符串是否在切片中
func Contains(slice []string, str string) bool {
	for _, item := range slice {
		if item == str {
			return true
		}
	}
	return false
}

func GenSaveImageTag(tag string) string {

	// 正则表达式：匹配时间部分（yyyyMMddHHmmss 格式）
	re := regexp.MustCompile(`_\d{14}$`)

	// 获取当前时间，格式化为 yyyyMMddHHmmss
	currentTime := time.Now().Format("20060102150405")

	// 检查是否匹配到符合条件的时间部分
	if re.MatchString(tag) {
		// 如果匹配到时间部分，则替换
		tag = re.ReplaceAllString(tag, "_"+currentTime)
	} else {
		// 如果没有匹配到，则在末尾加上 _currentTime
		tag = tag + "_" + currentTime
	}
	return tag
}
