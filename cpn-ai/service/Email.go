package service

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"errors"
	"fmt"
	"runtime"
	"strings"
	"sync"
	"time"

	"gopkg.in/gomail.v2"
)

type email_ struct {
	SameSubject map[string]time.Time `json:"same_subject"`
	NeedSend    sync.Map
}

var EmailService email_

type EmailReq struct {
	From        string `json:"from"`
	To          string `json:"to"`
	Subject     string `json:"subject"`
	Content     string `json:"content"`
	ContentType string `json:"content_type"`
}

type emailItem struct {
	ID        uint      `json:"id"`
	Type      int       `json:"type"`
	Content   string    `json:"content"`
	UpdatedAt time.Time `json:"-"`
}

type IgnoreSendItem struct {
	Key      string `json:"key"`
	ExpireAt int64  `json:"expire_at"`
	TryCount int    `json:"try_count"`
	SendAt   int64  `json:"send_at"`
	Content  any    `json:"content"`
}

func (obj *email_) AddNeedSend(key string, value interface{}) {
	obj.NeedSend.Store(key, value)
}

//func (obj *email_) AddNeedSend(key string, value interface{}) {
//	if _, ok := obj.IgnoreSend.Load(key); !ok {
//		obj.NeedSend.Store(key, value)
//		ignore := IgnoreSendItem{
//			Key:      key,
//			ExpireAt: time.Now().Add(time.Hour).Unix(),
//			TryCount: 1,
//			SendAt:   time.Now().Unix(),
//			Content:  value,
//		}
//		obj.IgnoreSend.Store(key, ignore)
//	}
//}

func (obj *email_) ClearNeedSend() {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	tmpMap := make(map[string]interface{})
	count := 0
	obj.NeedSend.Range(func(key, value interface{}) bool {
		bSend := true
		stringKey := key.(string)
		redisKey := enums.RedisKeyEnum.AlarmKey + stringKey
		if val, _ := common.RedisGet(redisKey); val != "" {
			bSend = false
		}

		if bSend {
			tmpMap[stringKey] = value
			count++
			if count > 50 { //不能发太多
				return false
			}
		}
		obj.NeedSend.Delete(stringKey)
		return true // 继续遍历
	})
	//logger.Info("发送内容：", utils.GetJsonFromStruct(tmpMap))
	if len(tmpMap) > 0 {
		emailReq := EmailReq{
			From:    "",
			To:      "<EMAIL>",
			Subject: fmt.Sprintf("有%d条算云警报，发送时间:%s", len(tmpMap), jsontime.Now().String()),
			Content: utils.GetJsonFromStruct(tmpMap),
		}
		//logger.Info("发送EmailReq：", utils.GetJsonFromStruct(emailReq))
		if err := EmailService.SendWarn(emailReq); err != nil {
			logger.Error(err)
		}
	}
}

func (obj *email_) Test() error {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
			logger.Error(code, msg, e, result, e)
		}
	}()

	subject := `容器存在但是实例已关闭`
	body := "容器存在但是实例已关闭\n"
	body += "容器存在但是实例已关闭\n"
	body += `容器存在但是实例已关闭\\n`
	body += "容器存在但是实例已关闭\n"
	oReq := EmailReq{
		From:    "",
		To:      "<EMAIL>",
		Subject: subject,
		Content: body,
	}

	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", oReq.To)
	m.SetHeader("Subject", oReq.Subject)
	//m.SetBody("text/html", oReq.Content)
	m.SetBody("text/plain", oReq.Content)

	d := gomail.NewDialer("smtp.163.com", 25, "guluguluxiaozi", "QESLVYWCPGINYOJP")
	if err := d.DialAndSend(m); err != nil {
		msg = "邮件发送失败"
		logger.Error(msg, " ", oReq.Subject, " ", err)
		return err
	} else {
		logger.Info("邮件发送成功")
	}
	code = 0
	return nil
}

func (obj *email_) Send(oReq EmailReq) error {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	if strings.Contains(utils.GetLocalIP(), "200.3") {
		return nil
	}
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
			logger.Error(code, msg, e, result, e)
		}
	}()

	m := gomail.NewMessage()
	//m.SetHeader("From", "<EMAIL>")
	//m.SetHeader("To", "<EMAIL>")
	//m.SetHeader("Subject", "邮件发送测试")
	//m.SetBody("text/html", "邮件发送测试内容Example Message Body")

	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", oReq.To)
	m.SetHeader("Subject", oReq.Subject)
	//m.SetBody("text/html", oReq.Content)
	m.SetBody("text/plain", oReq.Content)

	d := gomail.NewDialer("smtp.163.com", 25, "guluguluxiaozi", "QESLVYWCPGINYOJP")
	if err := d.DialAndSend(m); err != nil {
		msg = "邮件发送失败"
		logger.Error(msg, " ", oReq.Subject, " ", err)
		return err
	} else {
		logger.Info("邮件发送成功")
	}
	code = 0
	return nil
}

func (obj *email_) SendWarn(oReq EmailReq) error {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	if strings.Contains(utils.GetLocalIP(), "200.3") {
		return nil
	}
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
			logger.Error(code, msg, e, result, e)
		}
	}()

	m := gomail.NewMessage()

	m.SetHeader("To", oReq.To)
	m.SetHeader("Subject", oReq.Subject+" ip:"+utils.GetLocalIP())
	content := strings.ReplaceAll(oReq.Content, `\\\"`, `"`)
	content = strings.ReplaceAll(content, `\\"`, `"`)
	content = strings.ReplaceAll(content, `\"`, `"`)
	//m.SetBody("text/html", oReq.Content)
	m.SetBody("text/plain", oReq.Content)

	var err error
	{
		m.SetHeader("From", "<EMAIL>")
		m.SetHeader("To", "<EMAIL>")
		d := gomail.NewDialer("smtp.163.com", 25, "guluguluxiaozi", "QESLVYWCPGINYOJP")
		if err = d.DialAndSend(m); err != nil {
			msg = "邮件发送失败"
			logger.Error(msg, " ", oReq.Subject, " ", err)
		} else {
			logger.Info("邮件发送成功")
		}
	}

	{
		m.SetHeader("From", "<EMAIL>")
		m.SetHeader("To", "<EMAIL>")
		d := gomail.NewDialer("smtp.feishu.cn", 465, "<EMAIL>", "psK3xJEkMchq6uwY")
		if err = d.DialAndSend(m); err != nil {
			msg = "邮件发送失败"
			logger.Error(msg, " ", oReq.Subject, " ", err)
		} else {
			logger.Info("邮件发送成功")
		}

	}
	if err == nil {
		code = 0
	}
	return err
}

func (obj *email_) SendFromService(oReq EmailReq) error {
	if oReq.ContentType == "" {
		oReq.ContentType = common.EmailContentTypeText
	}
	return obj.SendFromServiceWitContentType(oReq)
}

func (obj *email_) SendFromServiceWitContentType(oReq EmailReq) error {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	if strings.Contains(utils.GetLocalIP(), "200.3") {
		return nil
	}
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
			logger.Error(code, msg, e, result, e)
		}
	}()

	m := gomail.NewMessage()

	//m.SetHeader("From", "<EMAIL>")
	//m.SetHeader("To", oReq.To)
	//m.SetHeader("Subject", oReq.Subject)
	//m.SetBody("text/html", oReq.Content)
	//
	//d := gomail.NewDialer("smtp.163.com", 25, "monitor202411", "FZf4ZsMCEFyLjpmJ")
	//if err := d.DialAndSend(m); err != nil {
	//	msg = "邮件发送失败"
	//	logger.Error(msg, " ", oReq.Subject, " ", err)
	//	return err
	//} else {
	//	logger.Info("邮件发送成功")
	//}
	//code = 0
	//return nil

	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", oReq.To)
	m.SetHeader("Subject", oReq.Subject)
	//m.SetBody("text/html", oReq.Content)
	m.SetBody(oReq.ContentType, oReq.Content)

	d := gomail.NewDialer("smtp.feishu.cn", 465, "<EMAIL>", "psK3xJEkMchq6uwY")
	if err := d.DialAndSend(m); err != nil {
		msg = "邮件发送失败"
		logger.Error(msg, " ", oReq.Subject, " ", err)
		return err
	} else {
		logger.Info("邮件发送成功")
	}
	code = 0
	return nil
}

func (obj *email_) SendPro(oReq EmailReq, outSeconds int64) error {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
			logger.Error(code, msg, e, result, e)
		}
	}()

	if obj.SameSubject == nil {
		obj.SameSubject = make(map[string]time.Time)
	}

	if outSeconds > 0 {
		md5 := utils.GetMd5(oReq.Subject)
		if lastSendTime, ok := obj.SameSubject[md5]; ok {
			if lastSendTime.After(time.Now().Add(-time.Second * time.Duration(outSeconds))) {
				return errors.New("间隔时间未到")
			}
		} else {
			obj.SameSubject[md5] = time.Now()
		}
	}

	if strings.Contains(utils.GetLocalIP(), "200.3") {
		return nil
	}

	m := gomail.NewMessage()
	//m.SetHeader("From", "<EMAIL>")
	//m.SetHeader("To", "<EMAIL>")
	//m.SetHeader("Subject", "邮件发送测试")
	//m.SetBody("text/html", "邮件发送测试内容Example Message Body")

	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", oReq.To)
	m.SetHeader("Subject", oReq.Subject)
	//m.SetBody("text/html", oReq.Content)
	m.SetBody("text/plain", oReq.Content)

	d := gomail.NewDialer("smtp.163.com", 25, "guluguluxiaozi", "QESLVYWCPGINYOJP")
	if err := d.DialAndSend(m); err != nil {
		msg = "邮件发送失败"
		logger.Error(msg, err)
		return err
	} else {
		logger.Info("邮件发送成功")
	}
	code = 0
	return nil
}

/*
func (obj *email_) SendByFeishu(oReq EmailReq) error {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
			logger.Error(code, msg, e, result, e)
		}
	}()

	client := lark.NewClient("********************", "MRg7vLqNT9W4yDzGsJOVtbUvErSao522")
	// 创建请求对象
	req := larkmail.NewSendUserMailboxMessageReqBuilder().
		Message(larkmail.NewMessageBuilder().
			Subject(oReq.Subject).
			To([]*larkmail.MailAddress{
				larkmail.NewMailAddressBuilder().
					MailAddress(oReq.To).
					Name(`你好`).
					Build(),
			}).
			HeadFrom(larkmail.NewMailAddressBuilder().
				Name(`晨羽AI`).
				//MailAddress(`<EMAIL>`).
				Build()).
			BodyHtml(oReq.Content).
			//BodyPlainText(`xxxxx`).
			Build()).
		Build()

	// 发起请求
	if resp, err := client.Mail.UserMailboxMessage.Send(context.Background(), req, larkcore.WithUserAccessToken("**********************************************")); err != nil {
		logger.Error(err)
		return err
	} else if resp.Success() {
		return nil
	} else {
		err = errors.New(fmt.Sprintf("logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError)))
		logger.Error(err)
		return err
	}
}
*/
//成功开启POP3/SMTP服务，在第三方客户端登录时，登录密码输入以下授权密码
//QESLVYWCPGINYOJP
