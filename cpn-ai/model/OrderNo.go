package model

import (
	"cpn-ai/common"
	"cpn-ai/enums"
	"errors"
	"fmt"
	"time"
)

type orderNo_ struct {
}

func (obj orderNo_) NewByOrderType(orderType int, idx int64) (string, error) {
	dt := time.Now()
	timeStr := dt.Format("**************")

	if idx == 0 {
		idx, _ = common.RedisIncr(enums.RedisKeyEnum.AutoSerialNumber)
		if idx <= 0 {
			return "", errors.New("获取自增需要失败")
		}
	}
	idxStr := obj.sup(idx, 8)
	if orderType == enums.OrderTypeEnum.Withdraw {
		prefix := enums.OrderNoPrefixEnum.Withdraw
		return prefix + timeStr + idxStr, nil
	} else if orderType == enums.OrderTypeEnum.VerifyWithdrawAccount {
		prefix := enums.OrderNoPrefixEnum.VerifyWithdrawAccount
		return prefix + timeStr + idxStr, nil
	} else if orderType == enums.OrderTypeEnum.Reward || orderType == enums.OrderTypeEnum.RewardPodUsage {
		prefix := enums.OrderNoPrefixEnum.Reward
		return prefix + timeStr + idxStr, nil
	} else if orderType == enums.OrderTypeEnum.ManagerAdd || orderType == enums.OrderTypeEnum.ManagerCost {
		prefix := enums.OrderNoPrefixEnum.Manage
		return prefix + timeStr + idxStr, nil
	} else if orderType == enums.OrderTypeEnum.UserGift {
		prefix := enums.OrderNoPrefixEnum.Gift
		return prefix + timeStr + idxStr, nil
	} else if orderType == enums.OrderTypeEnum.Cost || orderType == enums.OrderTypeEnum.ImageStore || orderType == enums.OrderTypeEnum.CloudStore {
		prefix := enums.OrderNoPrefixEnum.Cost
		return prefix + timeStr + idxStr, nil
	} else if orderType == enums.OrderTypeEnum.CostINST {
		prefix := enums.OrderNoPrefixEnum.CostInst
		return prefix + timeStr + idxStr, nil
	} else if orderType == enums.OrderTypeEnum.RechargeBuy {
		prefix := enums.OrderNoPrefixEnum.Recharge
		return prefix + timeStr + idxStr, nil
	} else if orderType == enums.OrderTypeEnum.Subscribe {
		prefix := enums.OrderNoPrefixEnum.Subscribe
		return prefix + timeStr + idxStr, nil
	} else if orderType == enums.OrderTypeEnum.Refund {
		prefix := enums.OrderNoPrefixEnum.Refund
		return prefix + timeStr + idxStr, nil
	} else if orderType == enums.OrderTypeEnum.KolCost {
		prefix := enums.OrderNoPrefixEnum.KolCost
		return prefix + timeStr + idxStr, nil
	} else {
		return "", errors.New("未找到前缀参数")
	}
}

func (obj orderNo_) New(orderType int, timeKey string) (string, error) {
	idx := int64(0)
	if idx == 0 {
		idx, _ = common.RedisIncr(enums.RedisKeyEnum.AutoSerialNumber)
		if idx <= 0 {
			return "", errors.New("获取自增需要失败")
		}
	}
	idxStr := obj.sup(idx, 8)

	if orderType == enums.OrderTypeEnum.CostLLM {
		prefix := enums.OrderNoPrefixEnum.CostLLM
		return fmt.Sprintf("%s_%s_%s", prefix, timeKey, idxStr), nil
	} else {
		return "", errors.New("未找到前缀参数")
	}
}

// 对长度不足n的数字前面补0
func (obj orderNo_) sup(i int64, n int) string {
	m := fmt.Sprintf("%d", i)
	for len(m) < n {
		m = fmt.Sprintf("0%s", m)
	}
	return m
}

var OrderNo orderNo_
