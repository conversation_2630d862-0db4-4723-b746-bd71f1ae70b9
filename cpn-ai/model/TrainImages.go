package model

import (
	"gorm.io/gorm"
	"time"
)

type TrainImagesStatusEnum int

const (
	TrainImagesStatusWating  TrainImagesStatusEnum = 1
	TrainImagesStatusTagging                       = 2
	TrainImagesStatusTagged                        = 3
)

func TrainImagesStatusName(i TrainImagesStatusEnum) string {
	switch i {
	case TrainImagesStatusWating:
		return "待打标"
	case TrainImagesStatusTagging:
		return "正在排队打标"
	case TrainImagesStatusTagged:
		return "打标完成"
	}
	return ""
}

type TrainImages struct {
	gorm.Model
	Uuid      string                `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID;uniqueIndex"`
	UserId    uint                  `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;index"`
	Title     string                `json:"title" gorm:"type:varchar(50);not null;default:'';comment:文本"`
	Count     int                   `json:"count" gorm:"type:int;not null;default:0;comment:图片集数量"`
	LastUseAt time.Time             `json:"last_use_at" gorm:"type:datetime;default:'1900-01-01';comment:最后使用时间"`
	Remark    string                `json:"remark" gorm:"type:varchar(150);not null;default:'';comment:备注"`
	Status    TrainImagesStatusEnum `json:"status" gorm:"type:int;not null;default:0;comment:状态 1等待打标 2正在打标 3打标完成"`
}

func (TrainImages) TableName() string {
	return "T_TrainImages"
}

func (o *TrainImages) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *TrainImages) GetByUuid(uuid string) error {
	err := DB.Debug().First(o, "uuid = ?", uuid).Error
	return err
}

func (o *TrainImages) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}
		if _, ok := queryParm["order_no"]; ok {
			tx.Where("order_no=?", queryParm["order_no"])
		}
		if _, ok := queryParm["out_trade_no"]; ok {
			tx.Where("out_trade_no=?", queryParm["out_trade_no"])
		}
		if _, ok := queryParm["business_type"]; ok {
			tx.Where("business_type=?", queryParm["business_type"])
		}
		if _, ok := queryParm["order_id"]; ok {
			tx.Where("order_id=?", queryParm["order_id"])
		}
		if _, ok := queryParm["pay_status"]; ok {
			tx.Where("pay_status=?", queryParm["pay_status"])
		}
		tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Scan(dest)
	return total, tx.Error
}

func (o *TrainImages) SetCount(count int) error {
	return DB.Model(o).Updates(map[string]interface{}{"count": count}).Error
}

func (o *TrainImages) Delete() error {
	return DB.Debug().Delete(o).Error
}
