package model

import (
	"gorm.io/gorm"
	"time"
)

type TrainTagsStatusEnum int

const (
	TrainTagsStatusQueue   TrainTagsStatusEnum = 1
	TrainTagsStatusTagging                     = 2
	TrainTagsStatusTagged                      = 3
	TrainTagsStatusTagFail                     = 4
)

func TrainTagStatusName(i TrainTagsStatusEnum) string {
	switch i {
	case TrainTagsStatusQueue:
		return "排队中"
	case TrainTagsStatusTagging:
		return "打标中"
	case TrainTagsStatusTagged:
		return "打标完成"
	case TrainTagsStatusTagFail:
		return "打标失败" //预留
	}
	return ""
}

type TrainTags struct {
	gorm.Model
	Uuid         string    `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:训练集字符串ID;uniqueIndex"`
	UserId       uint      `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;index"`
	ImagesUuid   string    `json:"images_uuid" gorm:"type:varchar(50);not null;default:'';comment:原始图集字符串ID;index"`
	Title        string    `json:"title" gorm:"type:varchar(50);not null;default:'';comment:文本"`
	CropMethod   string    `json:"crop_method" gorm:"type:varchar(50);not null;default:'';comment:裁剪方式"`
	CropSize     string    `json:"crop_size" gorm:"type:varchar(250);not null;default:'';comment:裁剪尺寸224*224,512*512"`
	TagThreshold float64   `json:"tag_threshold" gorm:"type:float;not null;default:0;comment:打标阈值"`
	TagAlg       string    `json:"tag_alg" gorm:"type:json;comment:打标算法"`
	TriggerWord  string    `json:"trigger_word" gorm:"type:json;comment:模型触发词"`
	TagParams    string    `json:"tag_params" gorm:"type:json;comment:打标参数"`
	Count        int       `json:"count" gorm:"type:int;not null;default:0;comment:图片集数量"`
	Remark       string    `json:"remark" gorm:"type:varchar(150);not null;default:'';comment:备注"`
	PushAt       time.Time `json:"push_at" gorm:"type:datetime;default:'1900-01-01';comment:推送队列时间"`
	StartAt      time.Time `json:"start_at" gorm:"type:datetime;default:'1900-01-01';comment:打标开始时间"`
	EndAt        time.Time `json:"end_at" gorm:"type:datetime;default:'1900-01-01';comment:打标结束时间"`
	LastUseAt    time.Time `json:"last_use_at" gorm:"type:datetime;default:'1900-01-01';comment:最后使用时间"`
	Reason       string    `json:"reason" gorm:"type:varchar(150);not null;default:'';comment:原因"`
	Status       int       `json:"status" gorm:"type:int;not null;default:0;comment:状态 1排队中 2打标中 3打标完成 4打标失败"`
}

func (TrainTags) TableName() string {
	return "T_TrainTags"
}

func (o *TrainTags) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *TrainTags) GetByUuid(uuid string) error {
	err := DB.Debug().First(o, "uuid = ?", uuid).Error
	return err
}
