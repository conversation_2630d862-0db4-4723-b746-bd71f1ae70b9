package structs

type TrainCropTag struct {
	ImagesUuid string       `json:"images_uuid"` //原始图片集
	DirPath    string       `json:"dir_path"`    //打标文件夹目录
	FilePath   string       `json:"file_path"`   //打标文件目录,空为打标整个文件夹
	Param      CropTagParam `json:"param"`       //打标参数，包括裁剪参数
}

type CropTagParam struct {
	CropMethod   string  `json:"crop_method"`
	CropSize     string  `json:"crop_size"`
	TagThreshold float64 `json:"tag_threshold"`
	TagAlg       string  `json:"tag_alg"`
	TriggerWord  string  `json:"trigger_word"`
}
